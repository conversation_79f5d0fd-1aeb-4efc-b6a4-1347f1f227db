{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(python -m pytest rpc/trendinsight/integration_tests/test_real_api.py::TestRealAPIIntegration::test_query_user_self_info_async_real -v)", "Bash(python -m pytest rpc/trendinsight/tests/test_response_handling.py -v)", "<PERSON><PERSON>(python test:*)", "Bash(rm:*)", "<PERSON><PERSON>(make:*)", "Bash(black:*)", "Bash(python -m pytest rpc/trendinsight/integration_tests/test_real_api.py::TestRealAPIIntegration::test_query_user_self_info_async_real -v --tb=short)", "Bash(python -m pytest rpc/trendinsight/integration_tests/test_real_api.py::TestRealAPIIntegration::test_query_user_self_info_real -v -s)", "Bash(python -m pytest rpc/trendinsight/integration_tests/test_real_api.py::TestRealAPIIntegration::test_query_user_self_info_real rpc/trendinsight/integration_tests/test_real_api.py::TestRealAPIIntegration::test_query_user_self_info_async_real -v)", "Bash(python -m pytest rpc/trendinsight/integration_tests/test_real_api.py::TestRealAPIIntegration::test_query_user_self_info_real rpc/trendinsight/integration_tests/test_real_api.py::TestRealAPIIntegration::test_query_user_self_info_async_real -v --tb=short)", "Bash(python -m pytest rpc/trendinsight/tests/test_response_handling.py -v --tb=short)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "Bash(uv add:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pytest:*)", "<PERSON><PERSON>(mv:*)", "Bash(ruff check:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(__NEW_LINE__ echo -e \"\\n=== 列出这些测试文件 ===\")", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:help.aliyun.com)", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "mcp__serena__list_dir", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__serena__think_about_whether_you_are_done", "Bash(cp:*)"], "deny": []}}
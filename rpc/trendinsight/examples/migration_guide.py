"""
TrendInsight 迁移指南

展示如何从旧的 rpc/trendinsight 迁移到新的 rpc/trendinsight-new
"""

# ==================== 旧版本使用方式 ====================


def old_way_example():
    """旧版本使用方式示例（仅作对比，不要运行）"""

    # 旧版本需要手动管理很多参数
    """
    from rpc.trendinsight import TrendInsightClient
    from rpc.trendinsight.schemas import DarenSearchRequest
    
    # 需要手动配置cookies、代理等
    client = TrendInsightClient(
        cookies="手动设置的cookies字符串",
        proxies="手动设置的代理",
        user_agent="手动设置的UA",
        timeout=30.0,
        auto_generate_params=True,
    )
    
    try:
        # 需要使用异步上下文管理器
        async with client:
            # 查询用户信息
            user_info = await client.query_user_self_info()
            
            # 搜索达人
            request = DarenSearchRequest(keyword="美食", total=10)
            daren_result = await client.query_daren_sug_great_user_list(request)
            
            # 搜索视频
            video_result = await client.search_info_by_keyword(
                keyword="美食",
                author_ids=None,
                category_id="0",
                date_type=0,
                label_type=0,
                duration_type=0,
            )
            
            # 获取作者详情
            author_detail = await client.get_author_detail("123456")
            
            # 获取视频指数
            video_index = await client.get_video_index(
                "video123", "20240101", "20240131"
            )
            
            # 测试连接
            is_connected = await client.pong()
    
    except Exception as e:
        logger.error(f"旧版本使用失败: {e}")
    """
    pass


# ==================== 新版本使用方式 ====================


def new_way_example():
    """新版本使用方式示例"""

    # 导入新版本组件
    from rpc.trendinsight import (  # 便捷函数（推荐）; 模型; API类（如需自定义）; 配置; 客户端管理器
        DarenSearchRequest,
        TrendInsightAPI,
        pong,
        query_daren_sug_great_user_list,
        query_user_self_info,
        search_info_by_keyword,
    )

    logger.info("=== 新版本使用方式 ===")

    # 方式1: 使用便捷函数（最简单）
    logger.info("\n1. 使用便捷函数:")
    try:
        # 直接调用，无需手动管理客户端
        user_info = query_user_self_info()
        logger.info(f"用户信息: {user_info.data.nickname if user_info.data else 'N/A'}")

        # 搜索达人
        request = DarenSearchRequest(keyword="美食", total=5)
        daren_result = query_daren_sug_great_user_list(request)
        logger.info(f"达人数量: {len(daren_result.userlist) if daren_result.userlist else 0}")

        # 搜索视频
        video_result = search_info_by_keyword(keyword="美食", date_type=1)
        logger.info(f"视频数量: {video_result.video_count}")

        # 测试连接
        is_connected = pong()
        logger.info(f"连接状态: {'正常' if is_connected else '异常'}")

    except Exception as e:
        logger.error(f"便捷函数使用失败: {e}")

    # 方式2: 使用API类（更多控制）
    logger.info("\n2. 使用API类:")
    try:
        # 创建API实例
        api = TrendInsightAPI()

        # 使用API实例
        user_info = api.query_user_self_info()
        logger.info("API类查询用户信息成功")

    except Exception as e:
        logger.error(f"API类使用失败: {e}")


def migration_comparison():
    """迁移对比表"""
    logger.info("\n=== 迁移对比 ===")

    comparisons = [
        {
            "功能": "导入模块",
            "旧版本": "from rpc.trendinsight import TrendInsightClient",
            "新版本": "from rpc.trendinsight import query_user_self_info",
        },
        {
            "功能": "客户端创建",
            "旧版本": "client = TrendInsightClient(cookies=..., proxies=...)",
            "新版本": "# 无需手动创建，使用便捷函数即可",
        },
        {
            "功能": "异步上下文",
            "旧版本": "async with client: ...",
            "新版本": "# 无需异步上下文，直接调用",
        },
        {
            "功能": "查询用户信息",
            "旧版本": "await client.query_user_self_info()",
            "新版本": "query_user_self_info()  # 同步调用",
        },
        {
            "功能": "搜索达人",
            "旧版本": "await client.query_daren_sug_great_user_list(request)",
            "新版本": "query_daren_sug_great_user_list(request)",
        },
        {
            "功能": "错误处理",
            "旧版本": "需要手动处理各种异步异常",
            "新版本": "统一的异常体系，更清晰的错误信息",
        },
        {
            "功能": "配置管理",
            "旧版本": "在客户端初始化时传入所有参数",
            "新版本": "使用 TrendInsightConfig 类统一管理",
        },
        {
            "功能": "资源管理",
            "旧版本": "需要手动管理客户端生命周期",
            "新版本": "自动管理，无需手动关闭",
        },
    ]

    for comp in comparisons:
        logger.info(f"\n📋 {comp['功能']}:")
        logger.info(f"  旧版本: {comp['旧版本']}")
        logger.info(f"  新版本: {comp['新版本']}")


def migration_steps():
    """迁移步骤"""
    logger.info("\n=== 迁移步骤 ===")

    steps = [
        {
            "步骤": 1,
            "标题": "更新导入语句",
            "说明": "将 'from rpc.trendinsight import ...' 改为 'from rpc.trendinsight import ...'",
            "示例": """
            # 旧版本
            from rpc.trendinsight import TrendInsightClient
            from rpc.trendinsight.schemas import DarenSearchRequest
            
            # 新版本
            from rpc.trendinsight import query_user_self_info, DarenSearchRequest
            """,
        },
        {
            "步骤": 2,
            "标题": "移除客户端管理代码",
            "说明": "删除手动创建和管理客户端的代码",
            "示例": """
            # 旧版本 - 删除这些代码
            client = TrendInsightClient(cookies=..., proxies=...)
            async with client:
                # API调用
            
            # 新版本 - 直接调用
            result = query_user_self_info()
            """,
        },
        {
            "步骤": 3,
            "标题": "移除异步语法",
            "说明": "将异步调用改为同步调用（或使用异步API类）",
            "示例": """
            # 旧版本
            user_info = await client.query_user_self_info()
            
            # 新版本（同步）
            user_info = query_user_self_info()
            
            # 新版本（异步，如需要）
            from rpc.trendinsight import AsyncTrendInsightAPI
            async_api = AsyncTrendInsightAPI()
            user_info = await async_api.query_user_self_info()
            """,
        },
        {
            "步骤": 4,
            "标题": "更新配置方式",
            "说明": "使用新的配置类管理配置",
            "示例": """
            # 旧版本
            client = TrendInsightClient(
                timeout=60.0,
                max_retries=5,
                verify_ssl=True
            )
            
            # 新版本
            from rpc.trendinsight import TrendInsightConfig, create_trendinsight_client
            config = TrendInsightConfig()
            config.timeout = 60.0
            config.max_retries = 5
            config.verify_ssl = True

            # 如需自定义客户端
            client = create_trendinsight_client(config=config, auto_generate_params=True)
            """,
        },
        {
            "步骤": 5,
            "标题": "更新异常处理",
            "说明": "使用新的异常类型",
            "示例": """
            # 旧版本
            from rpc.trendinsight.exceptions import TrendInsightAuthenticationError
            
            # 新版本
            from rpc.trendinsight.exceptions import TrendInsightAuthenticationError
            """,
        },
        {
            "步骤": 6,
            "标题": "测试迁移结果",
            "说明": "运行测试确保迁移成功",
            "示例": """
            # 测试基本功能
            from rpc.trendinsight import pong
            
            if pong():
                logger.info("✅ 迁移成功")
            else:
                logger.error("❌ 需要检查配置")
            """,
        },
    ]

    for step in steps:
        logger.info(f"\n{step['步骤']}. {step['标题']}")
        logger.info(f"   {step['说明']}")
        logger.info(f"   示例:{step['示例']}")


def migration_benefits():
    """迁移优势"""
    logger.info("\n=== 迁移优势 ===")

    benefits = [
        "🚀 更简单的API：无需手动管理客户端生命周期",
        "🔧 更好的架构：基于rpc.common的三步增强模式",
        "⚡ 更高的性能：优化的请求处理和缓存机制",
        "🛡️ 更强的稳定性：统一的错误处理和重试机制",
        "📝 更清晰的代码：职责分离，代码更易维护",
        "🔌 更好的扩展性：模块化设计，易于扩展",
        "📊 更丰富的监控：内置统计和性能监控",
        "🎯 更精确的类型：完整的类型注解支持",
    ]

    for benefit in benefits:
        logger.info(f"  {benefit}")


def migration_checklist():
    """迁移检查清单"""
    logger.info("\n=== 迁移检查清单 ===")

    checklist = [
        "□ 更新所有导入语句",
        "□ 移除客户端创建和管理代码",
        "□ 将异步调用改为同步调用",
        "□ 更新配置管理方式",
        "□ 更新异常处理代码",
        "□ 运行单元测试",
        "□ 运行集成测试",
        "□ 验证所有API功能正常",
        "□ 检查性能是否符合预期",
        "□ 更新相关文档",
    ]

    for item in checklist:
        logger.info(f"  {item}")


def main():
    """主函数"""
    logger.info("📚 TrendInsight 迁移指南")
    logger.info("=" * 50)

    # 新版本使用示例
    new_way_example()

    # 迁移对比
    migration_comparison()

    # 迁移步骤
    migration_steps()

    # 迁移优势
    migration_benefits()

    # 迁移检查清单
    migration_checklist()

    logger.info("\n✅ 迁移指南完成")
    logger.info("\n💡 迁移提示:")
    logger.info("  - 建议先在测试环境进行迁移验证")
    logger.info("  - 保留旧版本代码作为备份")
    logger.info("  - 逐步迁移，确保每个功能都正常工作")
    logger.info("  - 如遇到问题，请参考示例代码和文档")


if __name__ == "__main__":
    main()

"""
抖音视频控制器

专门处理抖音视频ID的完整业务逻辑，整合多种提取方法
"""

from typing import Any, Dict, Optional

from fastapi import HTTPException
from loguru import logger

from models.douyin.models import DouyinAweme
from utils.douyin.extract.douyin_data_extractor import (
    AwemeItem,
    DouyinDataExtractor,
    ProcessUrlResult,
)
from utils.douyin.extract.jingxuan_exceptions import (
    JingxuanNetworkError,
    JingxuanTimeoutError,
)

from .video_fetcher_controller import VideoFetcherController


class DouyinVideoController:
    """抖音视频控制器 - 处理视频ID的完整业务逻辑"""

    def __init__(self, douyin_controller=None):
        """
        初始化视频控制器

        Args:
            douyin_controller: 抖音主控制器实例，用于数据转换等功能
        """
        self.douyin_controller = douyin_controller
        self.video_fetcher = VideoFetcherController()

    async def process_video_id(self, aweme_id: str) -> Dict:
        """
        处理视频ID并提取相关数据

        提取管道优先级顺序：数据库 → 移动端URL → 精选 → RPC API

        Args:
            aweme_id: 抖音视频ID

        Returns:
            Dict: 处理后的视频数据
        """
        logger.info(f"开始处理视频ID: {aweme_id}")

        try:
            # 验证视频ID格式
            if not aweme_id.isdigit():
                logger.error(f"无效的视频ID格式: {aweme_id}")
                raise HTTPException(status_code=400, detail="无效的视频ID格式")

            # 步骤1: 先尝试从数据库获取
            logger.info(f"步骤1: 尝试从数据库获取视频数据, aweme_id: {aweme_id}")
            try:
                db_video = await DouyinAweme.filter(aweme_id=aweme_id).first()
                if db_video:
                    logger.info(f"数据库查询成功，找到视频数据, aweme_id: {aweme_id}")
                    return self._build_response(aweme_id, await db_video.to_dict(), "database")
                else:
                    logger.info(f"数据库中未找到视频数据，继续下一步提取, aweme_id: {aweme_id}")
            except Exception as e:
                logger.error(f"数据库查询失败, aweme_id: {aweme_id}, 错误: {str(e)}")

            # 步骤2: 通过移动端URL处理
            logger.info(f"步骤2: 尝试移动端URL提取, aweme_id: {aweme_id}")
            try:
                result = await self._process_by_mobile_url(aweme_id)
                if result:
                    logger.info(f"移动端URL提取成功, aweme_id: {aweme_id}")
                    return result
                else:
                    logger.warning(f"移动端URL提取失败，继续精选提取, aweme_id: {aweme_id}")
            except Exception as e:
                logger.error(f"移动端URL提取异常, aweme_id: {aweme_id}, 错误: {str(e)}")

            # 步骤3: 如果移动端处理失败，尝试精选提取
            logger.info(f"步骤3: 尝试精选提取, aweme_id: {aweme_id}")
            try:
                result = await self._process_by_jingxuan_url(aweme_id)
                if result:
                    logger.info(f"精选提取成功, aweme_id: {aweme_id}")
                    return result
                else:
                    logger.warning(f"精选提取失败，继续RPC API提取, aweme_id: {aweme_id}")
            except Exception as e:
                logger.error(f"精选提取异常, aweme_id: {aweme_id}, 错误: {str(e)}")

            # 步骤4: 如果精选提取也失败，则通过RPC接口获取
            logger.info(f"步骤4: 尝试RPC API提取, aweme_id: {aweme_id}")
            try:
                result = await self._process_by_rpc_api(aweme_id)
                logger.info(f"RPC API提取成功, aweme_id: {aweme_id}")
                return result
            except Exception as e:
                logger.error(f"RPC API提取失败, aweme_id: {aweme_id}, 错误: {str(e)}")
                raise HTTPException(status_code=500, detail=f"所有提取方法均失败，最后错误: {str(e)}")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"处理视频ID时发生未预期错误, aweme_id: {aweme_id}, 错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"处理视频ID失败: {str(e)}")

    async def _process_by_mobile_url(self, aweme_id: str) -> Optional[Dict]:
        """
        通过移动端URL处理视频ID

        Args:
            aweme_id: 抖音视频ID

        Returns:
            Optional[Dict]: 处理成功返回数据，失败返回None
        """
        from rpc.douyin.html_handler.url_manager import DouyinURLManager

        mobile_url = DouyinURLManager.build_mobile_share_url(aweme_id)
        logger.info(f"开始移动端URL提取, URL: {mobile_url}")

        try:
            with DouyinDataExtractor() as extractor:
                result: ProcessUrlResult = extractor.process_url(mobile_url)

                if not (result.success and result.video_info):
                    error_msg = result.error or "未知错误"
                    logger.warning(f"移动端URL HTML解析失败, aweme_id: {aweme_id}, 错误: {error_msg}")
                    return None

                aweme_item: AwemeItem = result.video_info
                logger.info(f"移动端URL HTML解析成功, aweme_id: {aweme_id}")

                # 验证下载链接有效性
                logger.info(f"验证移动端URL提取的下载链接有效性, aweme_id: {aweme_id}")
                if not await self._validate_download_url(aweme_item):
                    logger.warning(f"移动端URL提取的下载链接无效, aweme_id: {aweme_id}")
                    return None

                logger.info(f"移动端URL提取的下载链接验证通过, aweme_id: {aweme_id}")

                # 转换并保存数据
                logger.info(f"开始转换和保存移动端URL提取的数据, aweme_id: {aweme_id}")
                aweme_data = self.douyin_controller._convert_html_aweme_to_db_model(aweme_item, by_mobile=True)
                saved_data = await self._save_and_return_data(aweme_id, aweme_data, aweme_item, "mobile_extraction")

                logger.info(f"移动端URL提取完成, aweme_id: {aweme_id}")
                return saved_data

        except Exception as e:
            logger.error(f"移动端URL提取过程中发生异常, aweme_id: {aweme_id}, 错误: {str(e)}")
            return None

    async def _process_by_jingxuan_url(self, aweme_id: str) -> Optional[Dict]:
        """
        通过精选URL处理视频ID，使用 VideoFetcherController

        Args:
            aweme_id: 抖音视频ID

        Returns:
            Optional[Dict]: 处理成功返回数据，失败返回None
        """
        logger.info(f"[精选提取] 开始精选提取方法获取视频信息, aweme_id: {aweme_id}")

        try:
            # 使用 VideoFetcherController 获取精选数据
            fetch_result = await self.video_fetcher.fetch_video_data(aweme_id, "jingxuan")

            if not fetch_result.success:
                error_msg = fetch_result.error or "未知错误"
                logger.warning(f"[精选提取] 精选提取失败, aweme_id: {aweme_id}, 错误: {error_msg}")
                return None

            if not fetch_result.data:
                logger.warning(f"[精选提取] 精选提取成功但未获得视频信息, aweme_id: {aweme_id}")
                return None

            logger.info(f"[精选提取] 精选提取成功, aweme_id: {aweme_id}")

            # 验证数据质量
            logger.info(f"[精选提取] 开始验证精选提取数据质量, aweme_id: {aweme_id}")
            if not await self._validate_jingxuan_data_quality(fetch_result.data, aweme_id):
                logger.warning(f"[精选提取] 精选提取数据质量验证失败, aweme_id: {aweme_id}")
                return None

            logger.info(f"[精选提取] 精选提取数据质量验证通过, aweme_id: {aweme_id}")
            logger.info(f"[精选提取] 精选提取完成, aweme_id: {aweme_id}")

            return self._build_response(aweme_id, fetch_result.data, "jingxuan_extraction")

        except JingxuanNetworkError as e:
            logger.error(
                f"[精选提取] 网络错误, aweme_id: {aweme_id}, 状态码: {getattr(e, 'status_code', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except JingxuanTimeoutError as e:
            logger.error(
                f"[精选提取] 请求超时, aweme_id: {aweme_id}, 超时时间: {getattr(e, 'timeout_seconds', 'unknown')}秒, 错误: {str(e)}"
            )
            return None
        except JingxuanParsingError as e:
            logger.error(
                f"[精选提取] HTML解析错误, aweme_id: {aweme_id}, 解析阶段: {getattr(e, 'parsing_stage', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except JingxuanDataError as e:
            logger.error(
                f"[精选提取] 数据错误, aweme_id: {aweme_id}, 数据类型: {getattr(e, 'data_type', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except JingxuanConversionError as e:
            logger.error(
                f"[精选提取] 数据转换错误, aweme_id: {aweme_id}, 转换阶段: {getattr(e, 'conversion_stage', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except JingxuanValidationError as e:
            logger.error(
                f"[精选提取] 数据验证错误, aweme_id: {aweme_id}, 验证类型: {getattr(e, 'validation_type', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except JingxuanExtractionError as e:
            logger.error(
                f"[精选提取] 精选提取基础错误, aweme_id: {aweme_id}, 错误代码: {getattr(e, 'error_code', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except Exception as e:
            logger.error(
                f"[精选提取] 精选提取过程中发生未知异常, aweme_id: {aweme_id}, 异常类型: {type(e).__name__}, 错误: {str(e)}"
            )
            return None

    async def _process_by_rpc_api(self, aweme_id: str) -> Dict:
        """
        通过RPC接口处理视频ID，使用 VideoFetcherController

        Args:
            aweme_id: 抖音视频ID

        Returns:
            Dict: 处理后的视频数据
        """
        logger.info(f"开始RPC接口获取视频信息, aweme_id: {aweme_id}")

        try:
            # 使用 VideoFetcherController 获取RPC数据
            fetch_result = await self.video_fetcher.fetch_video_data(aweme_id, "rpc")

            if not fetch_result.success:
                error_msg = fetch_result.error or "RPC获取失败"
                logger.error(f"RPC API提取失败, aweme_id: {aweme_id}, 错误: {error_msg}")
                raise HTTPException(status_code=500, detail=f"RPC获取视频数据失败: {error_msg}")

            if not fetch_result.data:
                logger.error(f"RPC API提取成功但未获得视频信息, aweme_id: {aweme_id}")
                raise HTTPException(status_code=500, detail="RPC获取视频数据为空")

            logger.info(f"RPC接口获取视频数据成功, aweme_id: {aweme_id}")
            logger.info(f"RPC API提取完成, aweme_id: {aweme_id}")

            return self._build_response(aweme_id, fetch_result.data, "rpc_api")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"RPC API提取过程中发生异常, aweme_id: {aweme_id}, 错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"RPC获取视频数据失败: {str(e)}")

    async def _validate_jingxuan_data_quality(self, data: Dict[str, Any], aweme_id: str) -> bool:
        """
        验证精选提取数据质量

        Args:
            data: 从精选提取的视频数据
            aweme_id: 视频ID

        Returns:
            bool: 数据质量是否符合要求
        """
        logger.info(f"[精选提取] 开始验证精选数据质量, aweme_id: {aweme_id}")

        try:
            # 1. 验证aweme_id一致性
            extracted_id = str(data.get("aweme_id", ""))
            if extracted_id != aweme_id:
                logger.warning(f"[精选提取] aweme_id不匹配，期望: {aweme_id}, 实际: {extracted_id}")
                return False

            # 2. 验证必要字段存在
            required_fields = ["title", "desc", "author_name"]
            for field in required_fields:
                if not data.get(field):
                    logger.warning(f"[精选提取] 缺少必要字段: {field}, aweme_id: {aweme_id}")
                    return False

            # 3. 验证视频URL
            video_urls = data.get("video_urls", [])
            if not video_urls or not any(url and isinstance(url, str) and url.startswith("http") for url in video_urls):
                logger.warning(f"[精选提取] 没有找到有效的视频URL, aweme_id: {aweme_id}")
                return False

            # 4. 验证创建时间（如果存在）
            create_time = data.get("create_time")
            if create_time is not None:
                try:
                    create_time_int = int(create_time)
                    if create_time_int <= 0:
                        logger.warning(f"[精选提取] 创建时间无效: {create_time}, aweme_id: {aweme_id}")
                        return False
                except (ValueError, TypeError):
                    logger.warning(f"[精选提取] 创建时间格式无效: {create_time}, aweme_id: {aweme_id}")
                    return False

            logger.info(f"[精选提取] 精选数据质量验证全部通过, aweme_id: {aweme_id}")
            return True

        except Exception as e:
            logger.error(f"[精选提取] 验证精选数据质量时发生错误, aweme_id: {aweme_id}, 错误: {str(e)}")
            return False

    async def _validate_download_url(self, aweme_item: AwemeItem) -> bool:
        """
        验证下载链接有效性

        Args:
            aweme_item: 视频数据项

        Returns:
            bool: 链接是否有效
        """
        try:
            from models.douyin.models import extract_video_download_url
            from utils.douyin.url_processor import DouyinUrlProcessor

            # 1. 提取下载URL
            download_url = extract_video_download_url(aweme_item, True)
            if not download_url:
                logger.warning(f"无法提取下载URL, aweme_id: {aweme_item.get('aweme_id', 'unknown')}")
                return False

            # 2. 使用URL处理器验证URL可访问性
            url_processor = DouyinUrlProcessor()
            url_valid = url_processor.verify_video_download_url(download_url)

            if not url_valid:
                logger.warning(f"下载URL可访问性验证失败, URL: {download_url}")
                return False

            logger.info(f"下载URL验证通过, aweme_id: {aweme_item.get('aweme_id', 'unknown')}")
            return True

        except Exception as e:
            logger.error(f"验证下载URL时发生错误, aweme_id: {aweme_item.get('aweme_id', 'unknown')}, 错误: {str(e)}")
            return False

    async def _save_and_return_data(self, aweme_id: str, aweme_data: Dict, fallback_data: Any, source: str) -> Dict:
        """
        保存数据到数据库并返回响应

        Args:
            aweme_id: 视频ID
            aweme_data: 要保存的数据
            fallback_data: 保存失败时的备用数据
            source: 数据来源

        Returns:
            Dict: 响应数据
        """
        from models.douyin.models import update_douyin_aweme

        success = await update_douyin_aweme(aweme_data)

        if success:
            # 重新从数据库获取完整数据
            db_video = await DouyinAweme.filter(aweme_id=aweme_id).first()
            if db_video:
                return self._build_response(aweme_id, await db_video.to_dict(), source)

        # 保存失败，返回原始数据
        return self._build_response(aweme_id, fallback_data, source)

    def _build_response(self, aweme_id: str, data: Any, source: str) -> Dict:
        """
        构建统一的响应格式

        Args:
            aweme_id: 视频ID
            data: 响应数据
            source: 数据来源

        Returns:
            Dict: 格式化的响应
        """
        return {
            "video_id": aweme_id,
            "input_type": "aweme_id",
            "original_input": aweme_id,
            "processed": True,
            "data": data,
            "source": source,
        }

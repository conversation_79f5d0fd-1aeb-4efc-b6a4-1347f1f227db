#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音精选数据提取器
用于从抖音精选页面提取视频数据，使用RPC/douyin接口而非直接HTTP请求
"""

import json
import re
import time
import urllib.parse
from typing import Any, Dict, List, Optional

from loguru import logger

from .douyin_data_extractor import (
    AuthorInfo,
    AwemeItem,
    MusicInfo,
    RiskInfo,
    StatisticsInfo,
    TextExtraInfo,
    UrlInfo,
    VideoInfo,
)
from .jingxuan_exceptions import (
    JingxuanConversionError,
    JingxuanDataError,
    JingxuanExtractionError,
    JingxuanParsingError,
    JingxuanValidationError,
    create_error_context,
)


class JingxuanProcessResult:
    """精选提取过程的结果类"""

    def __init__(
        self,
        aweme_id: str,
        success: bool = False,
        raw_data: Optional[Dict] = None,
        video_info: Optional[AwemeItem] = None,
        error: Optional[str] = None,
    ):
        self.aweme_id = aweme_id
        self.success = success
        self.raw_data = raw_data
        self.video_info = video_info
        self.error = error

    def __getitem__(self, key: str):
        """支持字典式访问以保持向后兼容"""
        return getattr(self, key)

    def __setitem__(self, key: str, value):
        """支持字典式设置以保持向后兼容"""
        setattr(self, key, value)

    def __contains__(self, key: str) -> bool:
        """支持 in 操作符"""
        return hasattr(self, key)

    def get(self, key: str, default=None):
        """支持 get 方法以保持向后兼容"""
        return getattr(self, key, default)


class JingxuanDataExtractor:
    """精选URL模式数据提取器 - 使用RPC/douyin接口"""

    def __init__(self, timeout: int = 10):
        """
        初始化精选数据提取器

        Args:
            timeout: 请求超时时间（秒）
        """
        self.timeout = timeout
        # 移除HTTP客户端初始化，使用RPC接口

    def __enter__(self) -> "JingxuanDataExtractor":
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> bool:
        """上下文管理器退出"""
        # 移除close调用
        # 不抑制异常
        return False

    def close(self) -> None:
        """关闭客户端连接 - 不再需要"""
        pass

    async def fetch_video_data(self, aweme_id: str) -> Dict[str, Any]:
        """
        通过RPC接口获取精选视频数据

        Args:
            aweme_id: 抖音视频ID

        Returns:
            视频数据字典

        Raises:
            JingxuanDataError: 数据获取失败时抛出
        """
        operation_start_time = time.time()
        context = create_error_context(aweme_id, "fetch_video_data")

        logger.info("[精选提取] 开始通过RPC获取视频数据", extra=context)

        try:
            # 使用RPC接口获取视频数据
            from rpc.douyin import async_douyin_api
            from rpc.douyin.schemas import VideoDetailRequest

            request = VideoDetailRequest(aweme_id=aweme_id)
            response = await async_douyin_api.get_video_detail(request)

            if not response or not response.aweme_detail:
                error_msg = f"通过RPC未获取到视频数据: {aweme_id}"
                logger.error(f"[精选提取] RPC数据获取失败: {error_msg}", extra=context)
                raise JingxuanDataError(
                    error_msg, aweme_id=aweme_id, data_type="video_detail", validation_field="response"
                )

            success_context = {
                **context,
                "data_length": len(str(response.aweme_detail)),
                "processing_time": time.time() - operation_start_time,
            }

            logger.info("[精选提取] 通过RPC成功获取视频数据", extra=success_context)

            # 将RPC响应转换为标准格式
            return self._convert_rpc_response_to_json(response.aweme_detail)

        except JingxuanExtractionError:
            # 重新抛出已经格式化的精选提取错误
            raise
        except Exception as e:
            error_msg = f"通过RPC获取视频数据时发生未知错误: {e}"
            logger.error("[精选提取] RPC未知错误", extra={**context, "error_detail": str(e)})
            raise JingxuanDataError(error_msg, aweme_id=aweme_id, data_type="rpc_request", validation_field="execution")

    def extract_pace_f_data(self, html_content: str, aweme_id: Optional[str] = None) -> Optional[str]:
        """
        从HTML内容中提取包含指定aweme_id的pace_f条目数据，包含详细的错误处理和日志记录

        Args:
            html_content: HTML内容字符串
            aweme_id: 目标视频ID，用于匹配包含该ID的pace_f条目

        Returns:
            包含指定aweme_id的pace_f条目的编码数据，如果未找到则返回None

        Raises:
            JingxuanParsingError: HTML解析失败时抛出
            JingxuanDataError: 数据格式错误时抛出
        """
        operation_start_time = time.time()
        context = create_error_context(
            aweme_id or "unknown", "extract_pace_f_data", content_length=len(html_content) if html_content else 0
        )

        logger.info("[精选提取] 开始提取pace_f数据", extra=context)

        # 输入验证
        if not html_content:
            error_msg = "HTML内容为空"
            logger.error(f"[精选提取] HTML内容验证失败: {error_msg}", extra=context)
            raise JingxuanDataError(error_msg, aweme_id=aweme_id, data_type="html_content", validation_field="content")

        if not isinstance(html_content, str):
            error_msg = f"HTML内容必须是字符串类型，当前类型: {type(html_content)}"
            logger.error(f"[精选提取] HTML内容类型验证失败: {error_msg}", extra=context)
            raise JingxuanDataError(error_msg, aweme_id=aweme_id, data_type="html_content", validation_field="type")

        try:
            # 使用更严格的正则表达式查找所有self.__pace_f.push条目
            # 支持多种可能的格式：单引号、双引号、空格变化等
            patterns = [
                r"self\.__pace_f\.push\(\[(\d+),\s*[\"']([^\"']*)[\"']\]\)",  # 标准格式
                r"self\.__pace_f\.push\(\[\s*(\d+)\s*,\s*[\"']([^\"']*)[\"']\s*\]\)",  # 带更多空格
                r"window\.__pace_f\.push\(\[(\d+),\s*[\"']([^\"']*)[\"']\]\)",  # window前缀变体
                r"self\.__pace_f\.push\(\[(\d+),\s*`([^`]*)`\]\)",  # 反引号格式
                r"window\.__pace_f\.push\(\[\s*(\d+)\s*,\s*[\"']([^\"']*)[\"']\s*\]\)",  # window前缀带空格
            ]

            all_matches = []
            pattern_match_counts = {}

            logger.debug(f"[精选提取] 开始使用 {len(patterns)} 个正则表达式模式匹配pace_f条目", extra=context)

            for i, pattern in enumerate(patterns):
                try:
                    matches = re.findall(pattern, html_content, re.DOTALL)
                    pattern_match_counts[f"pattern_{i+1}"] = len(matches)
                    all_matches.extend(matches)

                    if matches:
                        logger.debug(
                            f"[精选提取] 模式 {i+1} 匹配到 {len(matches)} 个条目",
                            extra={**context, "pattern_index": i + 1, "matches_count": len(matches)},
                        )
                except re.error as e:
                    logger.warning(
                        f"[精选提取] 正则表达式模式 {i+1} 执行失败: {e}",
                        extra={**context, "pattern_index": i + 1, "regex_error": str(e)},
                    )
                    continue

            # 记录匹配统计信息
            match_context = {
                **context,
                "total_matches": len(all_matches),
                "pattern_matches": pattern_match_counts,
                "parsing_time": time.time() - operation_start_time,
            }

            if not all_matches:
                error_msg = "未找到任何pace_f.push条目"
                logger.error("[精选提取] pace_f条目匹配失败", extra=match_context)

                # 尝试诊断HTML内容
                self._diagnose_html_content(html_content, aweme_id)

                raise JingxuanParsingError(
                    error_msg, aweme_id=aweme_id, parsing_stage="pace_f_extraction", content_length=len(html_content)
                )

            logger.info(f"[精选提取] 成功匹配到 {len(all_matches)} 个pace_f条目", extra=match_context)

            # 查找包含目标aweme_id的条目
            target_entry_data = None
            found_indices = []
            checked_entries = []

            for index_str, data in all_matches:
                found_indices.append(index_str)
                logger.debug(
                    "[精选提取] 发现pace_f条目",
                    extra={
                        **context,
                        "entry_index": index_str,
                        "data_length": len(data),
                        "data_preview": data[:100] + "..." if len(data) > 100 else data,
                    },
                )

                # 如果提供了aweme_id，则检查当前条目是否包含该ID
                if aweme_id:
                    contains_aweme_id = self._check_entry_contains_aweme_id(data, aweme_id, index_str)
                    checked_entries.append(
                        {"index": index_str, "contains_target": contains_aweme_id, "data_length": len(data)}
                    )

                    if contains_aweme_id:
                        target_entry_data = data
                        logger.info(
                            "[精选提取] 找到包含目标aweme_id的pace_f条目",
                            extra={
                                **context,
                                "entry_index": index_str,
                                "target_aweme_id": aweme_id,
                                "data_length": len(data),
                            },
                        )
                        break
                else:
                    # 如果没有提供aweme_id，则回退到查找第6个条目的逻辑
                    if index_str == "6":
                        target_entry_data = data
                        logger.info(
                            "[精选提取] 未提供aweme_id，使用第6个pace_f条目",
                            extra={**context, "entry_index": index_str, "data_length": len(data)},
                        )
                        break

            # 记录所有找到的索引和检查结果
            search_context = {
                **context,
                "found_indices": found_indices,
                "target_aweme_id": aweme_id,
                "checked_entries": checked_entries if aweme_id else None,
                "search_mode": "aweme_id_match" if aweme_id else "sixth_entry_fallback",
            }

            if target_entry_data is None:
                if aweme_id:
                    error_msg = f"未找到包含aweme_id '{aweme_id}' 的pace_f条目，找到的索引: {found_indices}"
                    logger.error("[精选提取] 目标aweme_id匹配失败", extra=search_context)
                    raise JingxuanParsingError(
                        error_msg, aweme_id=aweme_id, parsing_stage="aweme_id_search", content_length=len(html_content)
                    )
                else:
                    error_msg = f"未找到第6个pace_f条目，找到的索引: {found_indices}"
                    logger.error("[精选提取] 第6个条目缺失", extra=search_context)
                    raise JingxuanParsingError(
                        error_msg,
                        aweme_id=aweme_id,
                        parsing_stage="sixth_entry_search",
                        content_length=len(html_content),
                    )

            # 验证数据不为空
            if not target_entry_data.strip():
                error_msg = "找到的pace_f条目数据为空"
                logger.error(
                    "[精选提取] pace_f条目数据为空",
                    extra={**search_context, "raw_data_length": len(target_entry_data)},
                )
                raise JingxuanDataError(
                    error_msg, aweme_id=aweme_id, data_type="pace_f_entry", validation_field="content"
                )

            # 验证数据格式（应该是URI编码的字符串）
            if not self._validate_pace_f_data_format(target_entry_data):
                logger.warning(
                    "[精选提取] pace_f数据格式可能异常",
                    extra={
                        **search_context,
                        "data_length": len(target_entry_data),
                        "data_preview": (
                            target_entry_data[:200] + "..." if len(target_entry_data) > 200 else target_entry_data
                        ),
                    },
                )

            success_context = {
                **search_context,
                "extraction_success": True,
                "data_length": len(target_entry_data),
                "total_processing_time": time.time() - operation_start_time,
            }

            logger.info("[精选提取] 成功提取包含目标aweme_id的pace_f条目", extra=success_context)
            return target_entry_data

        except re.error as e:
            error_msg = f"正则表达式执行错误: {e}"
            logger.error("[精选提取] 正则表达式错误", extra={**context, "regex_error": str(e)})
            raise JingxuanParsingError(
                error_msg, aweme_id=aweme_id, parsing_stage="regex_execution", content_length=len(html_content)
            )
        except JingxuanExtractionError:
            # 重新抛出已经格式化的精选提取错误
            raise
        except Exception as e:
            error_msg = f"提取pace_f数据时发生未知错误: {e}"
            logger.error("[精选提取] 未知错误", extra={**context, "error_detail": str(e)})
            raise JingxuanParsingError(
                error_msg, aweme_id=aweme_id, parsing_stage="unknown_error", content_length=len(html_content)
            )

    def _check_entry_contains_aweme_id(self, encoded_data: str, target_aweme_id: str, entry_index: str) -> bool:
        """
        检查pace_f条目是否包含目标aweme_id

        Args:
            encoded_data: URI编码的pace_f条目数据
            target_aweme_id: 目标aweme_id
            entry_index: 条目索引（用于日志记录）

        Returns:
            如果条目包含目标aweme_id则返回True，否则返回False
        """
        try:
            # 尝试解码数据
            decoded_data = self.decode_uri_component(encoded_data, target_aweme_id)

            # 检查解码后的数据是否包含目标aweme_id
            contains_id = target_aweme_id in decoded_data

            logger.debug(
                "[精选提取] 检查条目是否包含aweme_id",
                extra={
                    "aweme_id": target_aweme_id,
                    "entry_index": entry_index,
                    "contains_target": contains_id,
                    "decoded_length": len(decoded_data),
                    "decoded_preview": decoded_data[:200] + "..." if len(decoded_data) > 200 else decoded_data,
                },
            )

            return contains_id

        except Exception as e:
            logger.warning(
                "[精选提取] 检查条目时发生错误，跳过该条目",
                extra={
                    "aweme_id": target_aweme_id,
                    "entry_index": entry_index,
                    "error": str(e),
                    "encoded_data_length": len(encoded_data),
                },
            )
            return False

    def _validate_pace_f_data_format(self, data: str) -> bool:
        """
        验证pace_f数据格式是否符合预期

        Args:
            data: pace_f条目数据

        Returns:
            如果格式符合预期则返回True，否则返回False
        """
        try:
            # 检查是否包含URI编码的特征（%符号）
            has_uri_encoding = "%" in data

            # 检查是否包含JSON的特征（大括号）
            has_json_structure = "{" in data and "}" in data

            # 检查长度是否合理（太短可能不是有效数据）
            has_reasonable_length = len(data) > 50

            # 检查是否包含常见的视频数据字段关键词（URI编码后的）
            common_keywords = ["aweme", "video", "author", "desc"]
            has_video_keywords = any(keyword in data.lower() for keyword in common_keywords)

            # 至少满足其中几个条件
            conditions_met = sum([has_uri_encoding, has_json_structure, has_reasonable_length, has_video_keywords])

            return conditions_met >= 2

        except Exception:
            return False

    def _diagnose_html_content(self, html_content: str, aweme_id: Optional[str] = None) -> None:
        """
        诊断HTML内容，提供调试信息

        Args:
            html_content: HTML内容
            aweme_id: 视频ID
        """
        try:
            context = create_error_context(aweme_id or "unknown", "diagnose_html", content_length=len(html_content))

            # 检查HTML基本结构
            html_lower = html_content.lower()

            diagnostic_info = {
                "contains_html_tag": "<html" in html_lower,
                "contains_head_tag": "<head" in html_lower,
                "contains_body_tag": "<body" in html_lower,
                "contains_script_tag": "<script" in html_lower,
                "contains_pace_keyword": "pace" in html_lower,
                "contains_pace_f_keyword": "pace_f" in html_lower,
                "contains_push_keyword": "push" in html_lower,
                "content_length": len(html_content),
                "first_100_chars": html_content[:100],
                "last_100_chars": html_content[-100:] if len(html_content) > 100 else html_content,
            }

            logger.debug("[精选提取] HTML内容诊断信息", extra={**context, **diagnostic_info})

            # 检查是否可能是错误页面
            error_indicators = ["404", "not found", "error", "forbidden", "access denied"]
            found_errors = [indicator for indicator in error_indicators if indicator in html_lower]

            if found_errors:
                logger.warning(
                    "[精选提取] HTML内容可能包含错误页面指示器", extra={**context, "error_indicators": found_errors}
                )

            # 尝试查找可能的JavaScript错误
            if "error" in html_lower and "javascript" in html_lower:
                logger.warning("[精选提取] HTML内容可能包含JavaScript错误", extra=context)

        except Exception as e:
            logger.warning(
                f"[精选提取] HTML诊断过程中发生错误: {e}",
                extra={"aweme_id": aweme_id or "unknown", "diagnostic_error": str(e)},
            )

    def decode_uri_component(self, encoded_data: str, aweme_id: Optional[str] = None) -> str:
        """
        Python中等效的decodeURIComponent功能

        实现JavaScript decodeURIComponent的等效功能，包含完整的错误处理和日志记录

        Args:
            encoded_data: URI编码的数据
            aweme_id: 相关的视频ID（用于日志记录）

        Returns:
            解码后的字符串

        Raises:
            JingxuanDataError: 解码失败时抛出
        """
        operation_start_time = time.time()
        context = create_error_context(
            aweme_id or "unknown", "decode_uri_component", encoded_length=len(encoded_data) if encoded_data else 0
        )

        logger.info("[精选提取] 开始URI解码", extra=context)

        # 输入验证
        if not encoded_data:
            error_msg = "编码数据不能为空"
            logger.error(f"[精选提取] URI解码输入验证失败: {error_msg}", extra=context)
            raise JingxuanDataError(error_msg, aweme_id=aweme_id, data_type="uri_encoded", validation_field="content")

        if not isinstance(encoded_data, str):
            error_msg = f"编码数据必须是字符串类型，当前类型: {type(encoded_data)}"
            logger.error(f"[精选提取] URI解码类型验证失败: {error_msg}", extra=context)
            raise JingxuanDataError(error_msg, aweme_id=aweme_id, data_type="uri_encoded", validation_field="type")

        try:
            # 预处理：记录编码数据的统计信息
            encoding_stats = self._analyze_uri_encoding(encoded_data)
            stats_context = {**context, **encoding_stats}

            logger.debug("[精选提取] URI编码数据分析", extra=stats_context)

            # 先检查是否包含无效的百分号编码模式
            invalid_patterns = re.findall(r"%[^0-9A-Fa-f]|%[0-9A-Fa-f][^0-9A-Fa-f]|%$|%[0-9A-Fa-f]$", encoded_data)
            if invalid_patterns:
                error_msg = f"发现无效的百分号编码模式: {invalid_patterns[:5]}..."  # 只显示前5个
                logger.error(
                    "[精选提取] URI编码格式验证失败",
                    extra={
                        **context,
                        "invalid_patterns_count": len(invalid_patterns),
                        "invalid_patterns_sample": invalid_patterns[:5],
                    },
                )
                raise JingxuanDataError(
                    error_msg, aweme_id=aweme_id, data_type="uri_encoded", validation_field="encoding_format"
                )

            # 使用urllib.parse.unquote进行URI解码，等效于JavaScript的decodeURIComponent
            # unquote_plus=False 确保不将+号转换为空格，保持与decodeURIComponent一致
            logger.debug("[精选提取] 开始执行URI解码", extra=context)
            decoded = urllib.parse.unquote(encoded_data, encoding="utf-8", errors="strict")

            # 验证解码结果
            if not decoded:
                error_msg = "解码后的数据为空"
                logger.error("[精选提取] URI解码结果为空", extra=context)
                raise JingxuanDataError(
                    error_msg, aweme_id=aweme_id, data_type="uri_decoded", validation_field="content"
                )

            # 分析解码结果
            decode_analysis = self._analyze_decode_result(encoded_data, decoded)
            result_context = {**context, **decode_analysis, "decoding_time": time.time() - operation_start_time}

            # 检查解码是否成功（如果解码前后完全相同且包含%符号，可能解码失败）
            if decoded == encoded_data and "%" in encoded_data:
                logger.warning(
                    "[精选提取] 数据可能未被正确解码",
                    extra={
                        **result_context,
                        "original_preview": encoded_data[:100] + "..." if len(encoded_data) > 100 else encoded_data,
                    },
                )

            # 验证解码后的数据是否包含预期的JSON结构
            if not self._validate_decoded_data_structure(decoded):
                logger.warning("[精选提取] 解码后的数据结构可能异常", extra=result_context)

            logger.info("[精选提取] URI解码成功", extra=result_context)
            return decoded

        except UnicodeDecodeError as e:
            error_msg = f"URI解码时遇到编码错误: {e}"
            logger.error(
                "[精选提取] URI解码编码错误",
                extra={**context, "unicode_error": str(e), "error_position": getattr(e, "start", None)},
            )
            raise JingxuanDataError(
                error_msg, aweme_id=aweme_id, data_type="uri_encoded", validation_field="unicode_encoding"
            )
        except JingxuanExtractionError:
            # 重新抛出已经格式化的精选提取错误
            raise
        except Exception as e:
            error_msg = f"URI解码失败: {e}"
            logger.error("[精选提取] URI解码未知错误", extra={**context, "error_detail": str(e)})
            raise JingxuanDataError(
                error_msg, aweme_id=aweme_id, data_type="uri_encoded", validation_field="decoding_process"
            )

    def _analyze_uri_encoding(self, encoded_data: str) -> dict:
        """
        分析URI编码数据的统计信息

        Args:
            encoded_data: URI编码的数据

        Returns:
            统计信息字典
        """
        try:
            # 统计百分号编码的数量
            percent_count = encoded_data.count("%")

            # 统计不同类型的字符
            ascii_count = sum(1 for c in encoded_data if ord(c) < 128)
            non_ascii_count = len(encoded_data) - ascii_count

            # 检查是否包含常见的编码模式
            common_patterns = {
                "space_encoded": "%20" in encoded_data,
                "quote_encoded": "%22" in encoded_data,
                "brace_encoded": "%7B" in encoded_data or "%7D" in encoded_data,
                "colon_encoded": "%3A" in encoded_data,
                "slash_encoded": "%2F" in encoded_data,
            }

            return {
                "percent_encoding_count": percent_count,
                "ascii_char_count": ascii_count,
                "non_ascii_char_count": non_ascii_count,
                "encoding_density": percent_count / len(encoded_data) if encoded_data else 0,
                **common_patterns,
            }
        except Exception:
            return {"analysis_error": True}

    def _analyze_decode_result(self, original: str, decoded: str) -> dict:
        """
        分析解码结果

        Args:
            original: 原始编码数据
            decoded: 解码后的数据

        Returns:
            分析结果字典
        """
        try:
            return {
                "original_length": len(original),
                "decoded_length": len(decoded),
                "length_change": len(decoded) - len(original),
                "compression_ratio": len(decoded) / len(original) if original else 0,
                "contains_json_structure": "{" in decoded and "}" in decoded,
                "contains_chinese": any("\u4e00" <= c <= "\u9fff" for c in decoded),
                "decoded_preview": decoded[:200] + "..." if len(decoded) > 200 else decoded,
            }
        except Exception:
            return {"decode_analysis_error": True}

    def _validate_decoded_data_structure(self, decoded_data: str) -> bool:
        """
        验证解码后的数据结构是否符合预期

        Args:
            decoded_data: 解码后的数据

        Returns:
            如果结构符合预期则返回True，否则返回False
        """
        try:
            # 检查是否包含JSON结构
            if not ("{" in decoded_data and "}" in decoded_data):
                return False

            # 检查是否包含常见的视频数据字段
            video_keywords = ["aweme_id", "desc", "author", "video", "statistics"]
            found_keywords = sum(1 for keyword in video_keywords if keyword in decoded_data)

            # 至少包含2个关键字段
            return found_keywords >= 2

        except Exception:
            return False

    def validate_aweme_item_format(self, aweme_item: Dict[str, Any], aweme_id: Optional[str] = None) -> bool:
        """
        验证AwemeItem格式是否符合预期结构

        Args:
            aweme_item: 要验证的AwemeItem数据
            aweme_id: 相关的视频ID（用于日志记录）

        Returns:
            如果格式符合预期则返回True，否则返回False

        Raises:
            JingxuanValidationError: 验证失败时抛出
        """
        context = create_error_context(aweme_id or "unknown", "validate_aweme_item_format")
        logger.info("[精选提取] 开始验证AwemeItem格式", extra=context)

        try:
            # 1. 验证基本类型
            if not isinstance(aweme_item, dict):
                logger.error(
                    "[精选提取] AwemeItem不是字典类型", extra={**context, "actual_type": str(type(aweme_item))}
                )
                raise JingxuanValidationError(
                    "AwemeItem必须是字典类型",
                    aweme_id=aweme_id,
                    validation_type="item_type",
                    expected_value="dict",
                    actual_value=str(type(aweme_item)),
                )

            # 2. 验证必需字段
            required_fields = ["aweme_id", "desc", "author", "video"]
            missing_fields = [field for field in required_fields if field not in aweme_item]

            if missing_fields:
                logger.error("[精选提取] AwemeItem缺少必需字段", extra={**context, "missing_fields": missing_fields})
                raise JingxuanValidationError(
                    f"AwemeItem缺少必需字段: {', '.join(missing_fields)}",
                    aweme_id=aweme_id,
                    validation_type="required_fields",
                    expected_value=str(required_fields),
                    actual_value=str([f for f in required_fields if f in aweme_item]),
                )

            # 3. 验证aweme_id格式和一致性
            item_aweme_id = str(aweme_item.get("aweme_id", ""))
            if aweme_id and item_aweme_id != aweme_id:
                logger.warning(
                    "[精选提取] AwemeItem的aweme_id与请求的不匹配",
                    extra={**context, "expected_id": aweme_id, "actual_id": item_aweme_id},
                )
                # 这里只记录警告，不抛出异常，因为有些情况下可能是合法的

            # 4. 验证author字段结构
            author = aweme_item.get("author", {})
            if not isinstance(author, dict):
                logger.error("[精选提取] author字段不是字典类型", extra={**context, "actual_type": str(type(author))})
                raise JingxuanValidationError(
                    "author字段必须是字典类型",
                    aweme_id=aweme_id,
                    validation_type="author_type",
                    expected_value="dict",
                    actual_value=str(type(author)),
                )

            # 验证author必需字段
            author_required_fields = ["nickname", "sec_uid"]
            author_missing_fields = [field for field in author_required_fields if field not in author]

            if author_missing_fields:
                logger.warning(
                    "[精选提取] author缺少推荐字段", extra={**context, "missing_fields": author_missing_fields}
                )
                # 这里只记录警告，不抛出异常

            # 5. 验证video字段结构
            video = aweme_item.get("video", {})
            if not isinstance(video, dict):
                logger.error("[精选提取] video字段不是字典类型", extra={**context, "actual_type": str(type(video))})
                raise JingxuanValidationError(
                    "video字段必须是字典类型",
                    aweme_id=aweme_id,
                    validation_type="video_type",
                    expected_value="dict",
                    actual_value=str(type(video)),
                )

            # 验证video必需字段
            if "play_addr" not in video:
                logger.error("[精选提取] video缺少play_addr字段", extra=context)
                raise JingxuanValidationError(
                    "video字段缺少play_addr",
                    aweme_id=aweme_id,
                    validation_type="video_fields",
                    expected_value="play_addr",
                    actual_value=str(list(video.keys())),
                )

            # 验证play_addr结构
            play_addr = video.get("play_addr", {})
            if not isinstance(play_addr, dict) or "url_list" not in play_addr:
                logger.error(
                    "[精选提取] play_addr字段格式无效",
                    extra={
                        **context,
                        "play_addr_type": str(type(play_addr)),
                        "has_url_list": "url_list" in play_addr if isinstance(play_addr, dict) else False,
                    },
                )
                raise JingxuanValidationError(
                    "play_addr字段必须是包含url_list的字典",
                    aweme_id=aweme_id,
                    validation_type="play_addr_structure",
                    expected_value="dict with url_list",
                    actual_value=str(type(play_addr)),
                )

            # 验证url_list
            url_list = play_addr.get("url_list", [])
            if not isinstance(url_list, list) or len(url_list) == 0:
                logger.error(
                    "[精选提取] url_list为空或格式无效",
                    extra={
                        **context,
                        "url_list_type": str(type(url_list)),
                        "url_list_length": len(url_list) if isinstance(url_list, list) else 0,
                    },
                )
                raise JingxuanValidationError(
                    "url_list必须是非空列表",
                    aweme_id=aweme_id,
                    validation_type="url_list_structure",
                    expected_value="non-empty list",
                    actual_value=f"{str(type(url_list))}, length: {len(url_list) if isinstance(url_list, list) else 0}",
                )

            # 验证至少有一个有效URL
            valid_urls = [url for url in url_list if url and isinstance(url, str) and url.startswith("http")]
            if not valid_urls:
                logger.error(
                    "[精选提取] url_list中没有有效URL", extra={**context, "url_list": url_list[:3]}  # 只记录前3个URL
                )
                raise JingxuanValidationError(
                    "url_list中必须包含至少一个有效的HTTP URL",
                    aweme_id=aweme_id,
                    validation_type="valid_urls",
                    expected_value="http://... or https://...",
                    actual_value=str(url_list[:3]),
                )

            # 6. 验证统计信息（如果存在）
            statistics = aweme_item.get("statistics")
            if statistics is not None:
                if not isinstance(statistics, dict):
                    logger.warning(
                        "[精选提取] statistics字段不是字典类型",
                        extra={**context, "actual_type": str(type(statistics))},
                    )
                    # 这里只记录警告，不抛出异常
                else:
                    # 验证统计信息的aweme_id是否匹配
                    stats_aweme_id = str(statistics.get("aweme_id", ""))
                    if stats_aweme_id and aweme_id and stats_aweme_id != aweme_id:
                        logger.warning(
                            "[精选提取] statistics中的aweme_id与请求的不匹配",
                            extra={**context, "expected_id": aweme_id, "actual_id": stats_aweme_id},
                        )
                        # 这里只记录警告，不抛出异常

            # 7. 验证创建时间（如果存在）
            create_time = aweme_item.get("create_time")
            if create_time is not None:
                try:
                    create_time_int = int(create_time)
                    if create_time_int <= 0:
                        logger.warning(f"[精选提取] create_time值异常: {create_time}", extra=context)
                        # 这里只记录警告，不抛出异常
                except (ValueError, TypeError):
                    logger.warning(f"[精选提取] create_time格式无效: {create_time}", extra=context)
                    # 这里只记录警告，不抛出异常

            logger.info("[精选提取] AwemeItem格式验证通过", extra=context)
            return True

        except JingxuanValidationError:
            # 重新抛出已经格式化的验证错误
            raise
        except Exception as e:
            logger.error("[精选提取] AwemeItem验证过程中发生未知错误", extra={**context, "error": str(e)})
            raise JingxuanValidationError(
                f"AwemeItem验证失败: {str(e)}",
                aweme_id=aweme_id,
                validation_type="unknown_error",
                validation_context={"error_type": type(e).__name__},
            )

    def parse_json_data(self, decoded_data: str, aweme_id: Optional[str] = None) -> Dict[str, Any]:
        """
        解析解码后的JSON数据，包含完整的错误处理和数据验证

        Args:
            decoded_data: 解码后的JSON字符串
            aweme_id: 相关的视频ID（用于日志记录）

        Returns:
            解析后的JSON数据

        Raises:
            JingxuanDataError: JSON解析失败或数据格式无效时抛出
        """
        operation_start_time = time.time()
        context = create_error_context(
            aweme_id or "unknown", "parse_json_data", data_length=len(decoded_data) if decoded_data else 0
        )

        logger.info("[精选提取] 开始JSON解析", extra=context)

        # 输入验证
        if not decoded_data:
            error_msg = "解码后的数据不能为空"
            logger.error(f"[精选提取] JSON解析输入验证失败: {error_msg}", extra=context)
            raise JingxuanDataError(error_msg, aweme_id=aweme_id, data_type="json_string", validation_field="content")

        if not isinstance(decoded_data, str):
            error_msg = f"解码后的数据必须是字符串类型，当前类型: {type(decoded_data)}"
            logger.error(f"[精选提取] JSON解析类型验证失败: {error_msg}", extra=context)
            raise JingxuanDataError(error_msg, aweme_id=aweme_id, data_type="json_string", validation_field="type")

        # 清理数据：移除可能的前后空白字符
        cleaned_data = decoded_data.strip()
        if not cleaned_data:
            error_msg = "清理后的JSON数据为空"
            logger.error("[精选提取] JSON数据清理后为空", extra=context)
            raise JingxuanDataError(
                error_msg, aweme_id=aweme_id, data_type="json_string", validation_field="cleaned_content"
            )

        # 预处理：分析JSON数据特征
        json_analysis = self._analyze_json_structure(cleaned_data)
        analysis_context = {**context, **json_analysis}

        logger.debug("[精选提取] JSON数据结构分析", extra=analysis_context)

        try:
            # 尝试解析JSON
            logger.debug("[精选提取] 开始执行JSON解析", extra=context)
            parsed_data = json.loads(cleaned_data)

            # 验证解析结果是否为字典类型
            if not isinstance(parsed_data, dict):
                error_msg = f"JSON解析结果应为字典类型，实际类型: {type(parsed_data)}"
                logger.error(
                    "[精选提取] JSON解析结果类型错误",
                    extra={**context, "expected_type": "dict", "actual_type": str(type(parsed_data))},
                )
                raise JingxuanDataError(
                    error_msg, aweme_id=aweme_id, data_type="json_parsed", validation_field="result_type"
                )

            # 分析解析结果
            parse_result_analysis = self._analyze_parsed_json(parsed_data)
            result_context = {**context, **parse_result_analysis, "parsing_time": time.time() - operation_start_time}

            # 验证是否包含基本的视频信息结构
            structure_valid = self._validate_video_data_structure(parsed_data)
            if not structure_valid:
                logger.warning("[精选提取] 解析的JSON数据可能不包含有效的视频信息结构", extra=result_context)
                # 不抛出异常，只记录警告，因为数据结构可能有变化

            logger.info("[精选提取] JSON解析成功", extra=result_context)
            return parsed_data

        except json.JSONDecodeError as e:
            # 提供更详细的错误信息和上下文
            error_position = getattr(e, "pos", 0)
            error_context_str = self._get_json_error_context(cleaned_data, error_position)

            error_msg = f"JSON解析失败: {e.msg}"

            error_details = {
                **context,
                "json_error_msg": e.msg,
                "error_position": error_position,
                "error_context": error_context_str,
                "json_preview": cleaned_data[:500] + "..." if len(cleaned_data) > 500 else cleaned_data,
            }

            logger.error("[精选提取] JSON解析语法错误", extra=error_details)

            raise JingxuanDataError(
                f"{error_msg}. 错误位置附近内容: {error_context_str}",
                aweme_id=aweme_id,
                data_type="json_string",
                validation_field="json_syntax",
            )
        except JingxuanExtractionError:
            # 重新抛出已经格式化的精选提取错误
            raise
        except Exception as e:
            error_msg = f"JSON解析时发生未知错误: {e}"
            logger.error("[精选提取] JSON解析未知错误", extra={**context, "error_detail": str(e)})
            raise JingxuanDataError(
                error_msg, aweme_id=aweme_id, data_type="json_string", validation_field="parsing_process"
            )

    def _analyze_json_structure(self, json_str: str) -> dict:
        """
        分析JSON字符串的结构特征

        Args:
            json_str: JSON字符串

        Returns:
            结构分析结果字典
        """
        try:
            return {
                "json_length": len(json_str),
                "starts_with_brace": json_str.startswith("{"),
                "ends_with_brace": json_str.endswith("}"),
                "brace_count": json_str.count("{"),
                "bracket_count": json_str.count("["),
                "quote_count": json_str.count('"'),
                "contains_aweme": "aweme" in json_str.lower(),
                "contains_video": "video" in json_str.lower(),
                "contains_author": "author" in json_str.lower(),
                "estimated_nesting_level": min(json_str.count("{"), 10),  # 限制最大值避免过大
            }
        except Exception:
            return {"structure_analysis_error": True}

    def _analyze_parsed_json(self, parsed_data: dict) -> dict:
        """
        分析解析后的JSON数据

        Args:
            parsed_data: 解析后的JSON数据

        Returns:
            分析结果字典
        """
        try:
            # 统计顶级字段
            top_level_keys = list(parsed_data.keys())

            # 检查常见的视频数据字段
            video_fields = ["aweme_id", "desc", "author", "video", "statistics", "create_time"]
            found_video_fields = [field for field in video_fields if field in parsed_data]

            # 检查嵌套结构
            nested_objects = sum(1 for v in parsed_data.values() if isinstance(v, dict))
            nested_arrays = sum(1 for v in parsed_data.values() if isinstance(v, list))

            return {
                "top_level_field_count": len(top_level_keys),
                "top_level_keys": top_level_keys[:10],  # 只记录前10个键
                "found_video_fields": found_video_fields,
                "video_field_count": len(found_video_fields),
                "nested_object_count": nested_objects,
                "nested_array_count": nested_arrays,
                "has_aweme_structure": "aweme_id" in parsed_data or "aweme_detail" in parsed_data,
            }
        except Exception:
            return {"parsed_analysis_error": True}

    def _validate_video_data_structure(self, data: Dict[str, Any]) -> bool:
        """
        验证解析的数据是否包含所需的视频信息结构

        Args:
            data: 解析后的JSON数据

        Returns:
            如果数据包含基本的视频信息结构则返回True，否则返回False
        """
        try:
            # 检查常见的视频数据字段
            # 这些字段基于抖音API的常见响应结构
            expected_fields = [
                "aweme_id",  # 视频ID
                "desc",  # 视频描述
                "author",  # 作者信息
                "video",  # 视频信息
                "statistics",  # 统计信息
                "create_time",  # 创建时间
            ]

            # 检查是否至少包含一些关键字段
            found_fields = []
            for field in expected_fields:
                if field in data:
                    found_fields.append(field)

            # 如果找到至少2个关键字段，认为结构有效
            if len(found_fields) >= 2:
                logger.debug(f"发现有效的视频数据字段: {found_fields}")
                return True

            # 检查是否有嵌套的aweme_detail结构
            if "aweme_detail" in data and isinstance(data["aweme_detail"], dict):
                aweme_detail = data["aweme_detail"]
                detail_found_fields = []
                for field in expected_fields:
                    if field in aweme_detail:
                        detail_found_fields.append(field)

                if len(detail_found_fields) >= 2:
                    logger.debug(f"在aweme_detail中发现有效的视频数据字段: {detail_found_fields}")
                    return True

            # 检查是否有aweme_list结构
            if "aweme_list" in data and isinstance(data["aweme_list"], list) and len(data["aweme_list"]) > 0:
                first_aweme = data["aweme_list"][0]
                if isinstance(first_aweme, dict):
                    list_found_fields = []
                    for field in expected_fields:
                        if field in first_aweme:
                            list_found_fields.append(field)

                    if len(list_found_fields) >= 2:
                        logger.debug(f"在aweme_list中发现有效的视频数据字段: {list_found_fields}")
                        return True

            logger.warning(f"未发现足够的视频数据字段，找到的字段: {found_fields}")
            return False

        except Exception as e:
            logger.error(f"验证视频数据结构时发生错误: {e}")
            return False

    def _get_json_error_context(self, json_str: str, error_pos: int, context_length: int = 50) -> str:
        """
        获取JSON解析错误位置的上下文信息

        Args:
            json_str: JSON字符串
            error_pos: 错误位置
            context_length: 上下文长度

        Returns:
            错误位置附近的内容
        """
        try:
            start = max(0, error_pos - context_length)
            end = min(len(json_str), error_pos + context_length)
            context = json_str[start:end]

            # 标记错误位置
            if error_pos < len(json_str):
                relative_pos = error_pos - start
                if 0 <= relative_pos < len(context):
                    context = (
                        context[:relative_pos]
                        + ">>>"
                        + context[relative_pos : relative_pos + 1]
                        + "<<<"
                        + context[relative_pos + 1 :]
                    )

            return context
        except Exception:
            return "无法获取错误上下文"

    def validate_download_url(self, url: str, aweme_id: Optional[str] = None) -> bool:
        """
        验证下载URL是否符合预期格式和可访问性

        Args:
            url: 要验证的下载URL
            aweme_id: 相关的视频ID（用于日志记录）

        Returns:
            如果URL有效则返回True，否则返回False

        Raises:
            JingxuanValidationError: 验证失败时抛出
        """
        context = create_error_context(aweme_id or "unknown", "validate_download_url", url=url)
        logger.info("[精选提取] 开始验证下载URL", extra=context)

        try:
            # 1. 基本URL格式验证
            if not url or not isinstance(url, str):
                logger.error("[精选提取] 下载URL为空或类型无效", extra={**context, "url_type": str(type(url))})
                raise JingxuanValidationError(
                    "下载URL必须是非空字符串",
                    aweme_id=aweme_id,
                    validation_type="url_format",
                    expected_value="非空字符串",
                    actual_value=str(type(url)),
                )

            # 2. URL协议验证
            if not url.startswith(("http://", "https://")):
                logger.error(
                    "[精选提取] 下载URL协议无效", extra={**context, "url_prefix": url[:10] if len(url) >= 10 else url}
                )
                raise JingxuanValidationError(
                    "下载URL必须以http://或https://开头",
                    aweme_id=aweme_id,
                    validation_type="url_protocol",
                    expected_value="http:// or https://",
                    actual_value=url[:10] if len(url) >= 10 else url,
                )

            # 3. 抖音域名验证（可选，根据实际情况调整）
            valid_domains = ["douyin.com", "iesdouyin.com", "amemv.com", "bytecdn.cn", "bytedance.com"]
            if not any(domain in url for domain in valid_domains):
                logger.warning(
                    "[精选提取] 下载URL可能不是抖音官方域名", extra={**context, "valid_domains": valid_domains}
                )
                # 这里只记录警告，不抛出异常，因为可能使用了CDN或其他有效域名

            # 4. URL参数验证
            # 检查是否包含常见的视频URL参数
            common_params = ["video_id", "ratio", "line", "download"]
            has_common_params = any(f"{param}=" in url for param in common_params)

            if not has_common_params:
                logger.warning("[精选提取] 下载URL可能缺少常见参数", extra=context)
                # 这里只记录警告，不抛出异常

            # 5. URL长度验证
            if len(url) < 20:  # 抖音视频URL通常较长
                logger.warning(f"[精选提取] 下载URL长度异常短: {len(url)}字符", extra=context)
                # 这里只记录警告，不抛出异常

            logger.info("[精选提取] 下载URL格式验证通过", extra=context)
            return True

        except JingxuanValidationError:
            # 重新抛出已经格式化的验证错误
            raise
        except Exception as e:
            logger.error("[精选提取] 下载URL验证过程中发生未知错误", extra={**context, "error": str(e)})
            raise JingxuanValidationError(
                f"下载URL验证失败: {str(e)}",
                aweme_id=aweme_id,
                validation_type="unknown_error",
                validation_context={"error_type": type(e).__name__},
            )

    def validate_aweme_item_format(self, aweme_item: Dict[str, Any], aweme_id: Optional[str] = None) -> bool:
        """
        验证AwemeItem格式是否符合预期结构

        Args:
            aweme_item: 要验证的AwemeItem数据
            aweme_id: 相关的视频ID（用于日志记录）

        Returns:
            如果格式符合预期则返回True，否则返回False

        Raises:
            JingxuanValidationError: 验证失败时抛出
        """
        context = create_error_context(aweme_id or "unknown", "validate_aweme_item_format")
        logger.info("[精选提取] 开始验证AwemeItem格式", extra=context)

        try:
            # 1. 验证基本类型
            if not isinstance(aweme_item, dict):
                logger.error(
                    "[精选提取] AwemeItem不是字典类型", extra={**context, "actual_type": str(type(aweme_item))}
                )
                raise JingxuanValidationError(
                    "AwemeItem必须是字典类型",
                    aweme_id=aweme_id,
                    validation_type="item_type",
                    expected_value="dict",
                    actual_value=str(type(aweme_item)),
                )

            # 2. 验证必需字段
            required_fields = ["aweme_id", "desc", "author", "video"]
            missing_fields = [field for field in required_fields if field not in aweme_item]

            if missing_fields:
                logger.error("[精选提取] AwemeItem缺少必需字段", extra={**context, "missing_fields": missing_fields})
                raise JingxuanValidationError(
                    f"AwemeItem缺少必需字段: {', '.join(missing_fields)}",
                    aweme_id=aweme_id,
                    validation_type="required_fields",
                    expected_value=str(required_fields),
                    actual_value=str([f for f in required_fields if f in aweme_item]),
                )

            # 3. 验证aweme_id格式和一致性
            item_aweme_id = str(aweme_item.get("aweme_id", ""))
            if aweme_id and item_aweme_id != aweme_id:
                logger.warning(
                    "[精选提取] AwemeItem的aweme_id与请求的不匹配",
                    extra={**context, "expected_id": aweme_id, "actual_id": item_aweme_id},
                )
                # 这里只记录警告，不抛出异常，因为有些情况下可能是合法的

            # 4. 验证author字段结构
            author = aweme_item.get("author", {})
            if not isinstance(author, dict):
                logger.error("[精选提取] author字段不是字典类型", extra={**context, "actual_type": str(type(author))})
                raise JingxuanValidationError(
                    "author字段必须是字典类型",
                    aweme_id=aweme_id,
                    validation_type="author_type",
                    expected_value="dict",
                    actual_value=str(type(author)),
                )

            # 验证author必需字段
            author_required_fields = ["nickname", "sec_uid"]
            author_missing_fields = [field for field in author_required_fields if field not in author]

            if author_missing_fields:
                logger.warning(
                    "[精选提取] author缺少推荐字段", extra={**context, "missing_fields": author_missing_fields}
                )
                # 这里只记录警告，不抛出异常

            # 5. 验证video字段结构
            video = aweme_item.get("video", {})
            if not isinstance(video, dict):
                logger.error("[精选提取] video字段不是字典类型", extra={**context, "actual_type": str(type(video))})
                raise JingxuanValidationError(
                    "video字段必须是字典类型",
                    aweme_id=aweme_id,
                    validation_type="video_type",
                    expected_value="dict",
                    actual_value=str(type(video)),
                )

            # 验证video必需字段
            if "play_addr" not in video:
                logger.error("[精选提取] video缺少play_addr字段", extra=context)
                raise JingxuanValidationError(
                    "video字段缺少play_addr",
                    aweme_id=aweme_id,
                    validation_type="video_fields",
                    expected_value="play_addr",
                    actual_value=str(list(video.keys())),
                )

            # 验证play_addr结构
            play_addr = video.get("play_addr", {})
            if not isinstance(play_addr, dict) or "url_list" not in play_addr:
                logger.error(
                    "[精选提取] play_addr字段格式无效",
                    extra={
                        **context,
                        "play_addr_type": str(type(play_addr)),
                        "has_url_list": "url_list" in play_addr if isinstance(play_addr, dict) else False,
                    },
                )
                raise JingxuanValidationError(
                    "play_addr字段必须是包含url_list的字典",
                    aweme_id=aweme_id,
                    validation_type="play_addr_structure",
                    expected_value="dict with url_list",
                    actual_value=str(type(play_addr)),
                )

            # 验证url_list
            url_list = play_addr.get("url_list", [])
            if not isinstance(url_list, list) or len(url_list) == 0:
                logger.error(
                    "[精选提取] url_list为空或格式无效",
                    extra={
                        **context,
                        "url_list_type": str(type(url_list)),
                        "url_list_length": len(url_list) if isinstance(url_list, list) else 0,
                    },
                )
                raise JingxuanValidationError(
                    "url_list必须是非空列表",
                    aweme_id=aweme_id,
                    validation_type="url_list_structure",
                    expected_value="non-empty list",
                    actual_value=f"{str(type(url_list))}, length: {len(url_list) if isinstance(url_list, list) else 0}",
                )

            # 验证至少有一个有效URL
            valid_urls = [url for url in url_list if url and isinstance(url, str) and url.startswith("http")]
            if not valid_urls:
                logger.error(
                    "[精选提取] url_list中没有有效URL", extra={**context, "url_list": url_list[:3]}  # 只记录前3个URL
                )
                raise JingxuanValidationError(
                    "url_list中必须包含至少一个有效的HTTP URL",
                    aweme_id=aweme_id,
                    validation_type="valid_urls",
                    expected_value="http://... or https://...",
                    actual_value=str(url_list[:3]),
                )

            # 6. 验证统计信息（如果存在）
            statistics = aweme_item.get("statistics")
            if statistics is not None:
                if not isinstance(statistics, dict):
                    logger.warning(
                        "[精选提取] statistics字段不是字典类型",
                        extra={**context, "actual_type": str(type(statistics))},
                    )
                    # 这里只记录警告，不抛出异常
                else:
                    # 验证统计信息的aweme_id是否匹配
                    stats_aweme_id = str(statistics.get("aweme_id", ""))
                    if stats_aweme_id and aweme_id and stats_aweme_id != aweme_id:
                        logger.warning(
                            "[精选提取] statistics中的aweme_id与请求的不匹配",
                            extra={**context, "expected_id": aweme_id, "actual_id": stats_aweme_id},
                        )
                        # 这里只记录警告，不抛出异常

            # 7. 验证创建时间（如果存在）
            create_time = aweme_item.get("create_time")
            if create_time is not None:
                try:
                    create_time_int = int(create_time)
                    if create_time_int <= 0:
                        logger.warning(f"[精选提取] create_time值异常: {create_time}", extra=context)
                        # 这里只记录警告，不抛出异常
                except (ValueError, TypeError):
                    logger.warning(f"[精选提取] create_time格式无效: {create_time}", extra=context)
                    # 这里只记录警告，不抛出异常

            logger.info("[精选提取] AwemeItem格式验证通过", extra=context)
            return True

        except JingxuanValidationError:
            # 重新抛出已经格式化的验证错误
            raise
        except Exception as e:
            logger.error("[精选提取] AwemeItem验证过程中发生未知错误", extra={**context, "error": str(e)})
            raise JingxuanValidationError(
                f"AwemeItem验证失败: {str(e)}",
                aweme_id=aweme_id,
                validation_type="unknown_error",
                validation_context={"error_type": type(e).__name__},
            )

    def convert_to_aweme_item(self, json_data: Dict[str, Any], aweme_id: Optional[str] = None) -> Optional[AwemeItem]:
        """
        将精选JSON数据转换为AwemeItem格式，包含详细的错误处理和日志记录

        Args:
            json_data: 从精选页面提取的JSON数据
            aweme_id: 相关的视频ID（用于日志记录）

        Returns:
            转换后的AwemeItem，如果转换失败则返回None

        Raises:
            JingxuanConversionError: 数据转换失败时抛出
        """
        operation_start_time = time.time()
        context = create_error_context(
            aweme_id or "unknown", "convert_to_aweme_item", json_keys_count=len(json_data) if json_data else 0
        )

        logger.info("[精选提取] 开始转换为AwemeItem", extra=context)

        # 输入验证
        if not json_data:
            error_msg = "JSON数据不能为空"
            logger.error(f"[精选提取] AwemeItem转换输入验证失败: {error_msg}", extra=context)
            raise JingxuanConversionError(error_msg, aweme_id=aweme_id, conversion_stage="input_validation")

        if not isinstance(json_data, dict):
            error_msg = f"JSON数据必须是字典类型，当前类型: {type(json_data)}"
            logger.error(f"[精选提取] AwemeItem转换类型验证失败: {error_msg}", extra=context)
            raise JingxuanConversionError(error_msg, aweme_id=aweme_id, conversion_stage="type_validation")

        try:
            # 步骤1: 从不同的数据结构中提取aweme数据
            logger.debug("[精选提取] 开始提取aweme数据", extra=context)
            aweme_data = self._extract_aweme_data_from_json(json_data)

            if not aweme_data:
                error_msg = "无法从JSON数据中提取有效的aweme数据"
                logger.error(
                    "[精选提取] aweme数据提取失败",
                    extra={
                        **context,
                        "available_keys": list(json_data.keys())[:10],  # 只记录前10个键
                        "json_structure_type": self._identify_json_structure_type(json_data),
                    },
                )
                raise JingxuanConversionError(error_msg, aweme_id=aweme_id, conversion_stage="aweme_data_extraction")

            logger.debug(
                "[精选提取] 成功提取aweme数据", extra={**context, "aweme_data_keys": list(aweme_data.keys())[:10]}
            )

            # 步骤2: 验证必需字段
            logger.debug("[精选提取] 开始验证必需字段", extra=context)
            missing_fields = self._validate_required_fields_detailed(aweme_data)

            if missing_fields:
                error_msg = f"aweme数据缺少必需字段: {missing_fields}"
                logger.error(
                    "[精选提取] 必需字段验证失败",
                    extra={
                        **context,
                        "missing_fields": missing_fields,
                        "available_fields": list(aweme_data.keys())[:10],
                    },
                )
                raise JingxuanConversionError(
                    error_msg,
                    aweme_id=aweme_id,
                    conversion_stage="required_fields_validation",
                    missing_fields=missing_fields,
                )

            logger.debug("[精选提取] 必需字段验证通过", extra=context)

            # 步骤3: 构建AwemeItem
            logger.debug("[精选提取] 开始构建AwemeItem", extra=context)
            aweme_item = self._build_aweme_item_with_error_handling(aweme_data, aweme_id)

            if not aweme_item:
                error_msg = "构建AwemeItem失败"
                logger.error("[精选提取] AwemeItem构建失败", extra=context)
                raise JingxuanConversionError(error_msg, aweme_id=aweme_id, conversion_stage="aweme_item_building")

            # 步骤4: 验证构建结果
            validation_result = self._validate_aweme_item(aweme_item, aweme_id)

            success_context = {
                **context,
                "conversion_success": True,
                "aweme_item_id": aweme_item.get("aweme_id", "unknown"),
                "conversion_time": time.time() - operation_start_time,
                **validation_result,
            }

            logger.info("[精选提取] 成功转换为AwemeItem", extra=success_context)
            return aweme_item

        except JingxuanExtractionError:
            # 重新抛出已经格式化的精选提取错误
            raise
        except Exception as e:
            error_msg = f"转换为AwemeItem时发生未知错误: {e}"
            logger.error(
                "[精选提取] AwemeItem转换未知错误",
                extra={**context, "error_detail": str(e), "error_type": type(e).__name__},
            )
            raise JingxuanConversionError(error_msg, aweme_id=aweme_id, conversion_stage="unknown_error")

    def _identify_json_structure_type(self, json_data: dict) -> str:
        """
        识别JSON数据的结构类型

        Args:
            json_data: JSON数据

        Returns:
            结构类型描述
        """
        try:
            if "aweme_id" in json_data:
                return "direct_aweme"
            elif "aweme_detail" in json_data:
                return "aweme_detail_wrapper"
            elif "aweme_list" in json_data:
                return "aweme_list_wrapper"
            elif "item_list" in json_data:
                return "item_list_wrapper"
            else:
                return "unknown_structure"
        except Exception:
            return "analysis_error"

    def _validate_required_fields_detailed(self, aweme_data: Dict[str, Any]) -> List[str]:
        """
        详细验证aweme数据是否包含必需字段，返回缺失的字段列表

        Args:
            aweme_data: aweme数据

        Returns:
            缺失的字段列表
        """
        required_fields = ["aweme_id"]
        missing_fields = []

        for field in required_fields:
            if field not in aweme_data or not aweme_data[field]:
                missing_fields.append(field)

        return missing_fields

    def _build_aweme_item_with_error_handling(
        self, aweme_data: Dict[str, Any], aweme_id: Optional[str] = None
    ) -> Optional[AwemeItem]:
        """
        带错误处理的AwemeItem构建方法

        Args:
            aweme_data: 原始aweme数据
            aweme_id: 视频ID

        Returns:
            构建的AwemeItem，如果构建失败则返回None
        """
        try:
            context = create_error_context(aweme_id or "unknown", "build_aweme_item")

            # 构建基本信息
            logger.debug("[精选提取] 构建基本信息", extra=context)
            aweme_item: AwemeItem = {
                "aweme_id": str(aweme_data.get("aweme_id", "")),
                "desc": str(aweme_data.get("desc", "")),
                "create_time": self._safe_int(aweme_data.get("create_time", 0)),
                "aweme_type": self._safe_int(aweme_data.get("aweme_type", 0)),
                "group_id_str": str(aweme_data.get("group_id", aweme_data.get("aweme_id", ""))),
            }

            # 构建作者信息
            logger.debug("[精选提取] 构建作者信息", extra=context)
            try:
                aweme_item["author"] = self._build_author_info(aweme_data.get("author", {}))
            except Exception as e:
                logger.warning(f"[精选提取] 构建作者信息失败: {e}", extra=context)
                aweme_item["author"] = self._build_author_info({})  # 使用默认值

            # 构建音乐信息
            logger.debug("[精选提取] 构建音乐信息", extra=context)
            try:
                aweme_item["music"] = self._build_music_info(aweme_data.get("music", {}))
            except Exception as e:
                logger.warning(f"[精选提取] 构建音乐信息失败: {e}", extra=context)
                aweme_item["music"] = self._build_music_info({})  # 使用默认值

            # 构建视频信息
            logger.debug("[精选提取] 构建视频信息", extra=context)
            try:
                aweme_item["video"] = self._build_video_info(aweme_data.get("video", {}))
            except Exception as e:
                logger.warning(f"[精选提取] 构建视频信息失败: {e}", extra=context)
                aweme_item["video"] = self._build_video_info({})  # 使用默认值

            # 构建统计信息
            logger.debug("[精选提取] 构建统计信息", extra=context)
            try:
                aweme_item["statistics"] = self._build_statistics_info(
                    aweme_data.get("statistics", {}), aweme_item["aweme_id"]
                )
            except Exception as e:
                logger.warning(f"[精选提取] 构建统计信息失败: {e}", extra=context)
                aweme_item["statistics"] = self._build_statistics_info({}, aweme_item["aweme_id"])

            # 构建文本额外信息（hashtags等）
            logger.debug("[精选提取] 构建文本额外信息", extra=context)
            try:
                aweme_item["text_extra"] = self._build_text_extra_info(aweme_data.get("text_extra", []))
            except Exception as e:
                logger.warning(f"[精选提取] 构建文本额外信息失败: {e}", extra=context)
                aweme_item["text_extra"] = []

            # 构建风险信息
            logger.debug("[精选提取] 构建风险信息", extra=context)
            try:
                aweme_item["risk_infos"] = self._build_risk_info(aweme_data.get("risk_infos", {}))
            except Exception as e:
                logger.warning(f"[精选提取] 构建风险信息失败: {e}", extra=context)
                aweme_item["risk_infos"] = self._build_risk_info({})

            # 设置可选字段为None（保持与类型定义一致）
            optional_fields = [
                "cha_list",
                "video_labels",
                "image_infos",
                "comment_list",
                "geofencing",
                "video_text",
                "label_top_text",
                "promotions",
                "long_video",
                "images",
                "chapter_list",
                "interaction_stickers",
                "img_bitrate",
                "chapter_bar_color",
            ]

            for field in optional_fields:
                aweme_item[field] = aweme_data.get(field)

            logger.debug("[精选提取] AwemeItem构建完成", extra=context)
            return aweme_item

        except Exception as e:
            logger.error(
                f"[精选提取] 构建AwemeItem时发生错误: {e}",
                extra={"aweme_id": aweme_id or "unknown", "error_detail": str(e), "error_type": type(e).__name__},
            )
            return None

    def _validate_aweme_item(self, aweme_item: AwemeItem, aweme_id: Optional[str] = None) -> dict:
        """
        验证构建的AwemeItem

        Args:
            aweme_item: 构建的AwemeItem
            aweme_id: 视频ID

        Returns:
            验证结果字典
        """
        try:
            validation_result = {
                "has_aweme_id": bool(aweme_item.get("aweme_id")),
                "has_author": bool(aweme_item.get("author")),
                "has_video": bool(aweme_item.get("video")),
                "has_statistics": bool(aweme_item.get("statistics")),
                "aweme_id_matches": str(aweme_item.get("aweme_id", "")) == str(aweme_id) if aweme_id else True,
                "video_has_play_addr": bool(aweme_item.get("video", {}).get("play_addr", {}).get("url_list")),
                "author_has_nickname": bool(aweme_item.get("author", {}).get("nickname")),
            }

            # 计算验证通过的项目数
            validation_result["validation_passed_count"] = sum(
                1 for v in validation_result.values() if isinstance(v, bool) and v
            )
            validation_result["validation_total_count"] = sum(
                1 for v in validation_result.values() if isinstance(v, bool)
            )

            return validation_result

        except Exception as e:
            return {"validation_error": str(e)}

    def _extract_aweme_data_from_json(self, json_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        从JSON数据中提取aweme数据，支持多种数据结构
        针对精选页面的app.videoDetail结构进行优化

        Args:
            json_data: 原始JSON数据

        Returns:
            提取的aweme数据，如果未找到则返回None
        """
        try:
            # 策略1: 检查精选页面的app.videoDetail结构（新增）
            if "app" in json_data and isinstance(json_data["app"], dict):
                app_data = json_data["app"]
                if "videoDetail" in app_data and isinstance(app_data["videoDetail"], dict):
                    video_detail = app_data["videoDetail"]
                    # 将videoDetail结构转换为标准aweme数据结构
                    converted_aweme = self._convert_video_detail_to_aweme(video_detail)
                    if converted_aweme and self._is_aweme_data(converted_aweme):
                        return converted_aweme

            # 策略2: 直接检查是否为aweme数据结构
            if self._is_aweme_data(json_data):
                return json_data

            # 策略3: 检查aweme_detail字段
            if "aweme_detail" in json_data and isinstance(json_data["aweme_detail"], dict):
                aweme_detail = json_data["aweme_detail"]
                if self._is_aweme_data(aweme_detail):
                    return aweme_detail

            # 策略4: 检查aweme_list字段
            if "aweme_list" in json_data and isinstance(json_data["aweme_list"], list):
                aweme_list = json_data["aweme_list"]
                if len(aweme_list) > 0 and isinstance(aweme_list[0], dict):
                    first_aweme = aweme_list[0]
                    if self._is_aweme_data(first_aweme):
                        return first_aweme

            # 策略5: 检查item_list字段
            if "item_list" in json_data and isinstance(json_data["item_list"], list):
                item_list = json_data["item_list"]
                if len(item_list) > 0 and isinstance(item_list[0], dict):
                    first_item = item_list[0]
                    if self._is_aweme_data(first_item):
                        return first_item

            # 策略6: 递归搜索嵌套结构
            aweme_data = self._recursive_search_aweme_data(json_data)
            if aweme_data:
                return aweme_data

            return None

        except Exception as e:
            logger.error(f"提取aweme数据时发生错误: {e}")
            return None

    def _is_aweme_data(self, data: Dict[str, Any]) -> bool:
        """
        判断数据是否为有效的aweme数据结构

        Args:
            data: 待检查的数据

        Returns:
            如果是有效的aweme数据则返回True，否则返回False
        """
        if not isinstance(data, dict):
            return False

        # 检查关键字段
        required_fields = ["aweme_id"]
        optional_fields = ["desc", "author", "video", "statistics", "create_time"]

        # 必须包含aweme_id
        if "aweme_id" not in data:
            return False

        # 至少包含一些可选字段
        found_optional = sum(1 for field in optional_fields if field in data)
        return found_optional >= 2

    def _recursive_search_aweme_data(
        self, data: Any, max_depth: int = 3, current_depth: int = 0
    ) -> Optional[Dict[str, Any]]:
        """
        递归搜索aweme数据结构

        Args:
            data: 待搜索的数据
            max_depth: 最大搜索深度
            current_depth: 当前搜索深度

        Returns:
            找到的aweme数据，如果未找到则返回None
        """
        if current_depth >= max_depth:
            return None

        if isinstance(data, dict):
            # 检查当前字典是否为aweme数据
            if self._is_aweme_data(data):
                return data

            # 递归搜索字典的值
            for value in data.values():
                result = self._recursive_search_aweme_data(value, max_depth, current_depth + 1)
                if result:
                    return result

        elif isinstance(data, list):
            # 递归搜索列表中的元素
            for item in data:
                result = self._recursive_search_aweme_data(item, max_depth, current_depth + 1)
                if result:
                    return result

        return None

    def _validate_required_fields(self, aweme_data: Dict[str, Any]) -> bool:
        """
        验证aweme数据是否包含必需字段

        Args:
            aweme_data: aweme数据

        Returns:
            如果包含必需字段则返回True，否则返回False
        """
        required_fields = ["aweme_id"]

        for field in required_fields:
            if field not in aweme_data or not aweme_data[field]:
                logger.error(f"缺少必需字段: {field}")
                return False

        return True

    def _build_aweme_item(self, aweme_data: Dict[str, Any]) -> Optional[AwemeItem]:
        """
        根据aweme数据构建AwemeItem

        Args:
            aweme_data: 原始aweme数据

        Returns:
            构建的AwemeItem，如果构建失败则返回None
        """
        try:
            # 构建基本信息
            aweme_item: AwemeItem = {
                "aweme_id": str(aweme_data.get("aweme_id", "")),
                "desc": str(aweme_data.get("desc", "")),
                "create_time": self._safe_int(aweme_data.get("create_time", 0)),
                "aweme_type": self._safe_int(aweme_data.get("aweme_type", 0)),
                "group_id_str": str(aweme_data.get("group_id", aweme_data.get("aweme_id", ""))),
            }

            # 构建作者信息
            aweme_item["author"] = self._build_author_info(aweme_data.get("author", {}))

            # 构建音乐信息
            aweme_item["music"] = self._build_music_info(aweme_data.get("music", {}))

            # 构建视频信息
            aweme_item["video"] = self._build_video_info(aweme_data.get("video", {}))

            # 构建统计信息
            aweme_item["statistics"] = self._build_statistics_info(
                aweme_data.get("statistics", {}), aweme_item["aweme_id"]
            )

            # 构建文本额外信息（hashtags等）
            aweme_item["text_extra"] = self._build_text_extra_info(aweme_data.get("text_extra", []))

            # 构建风险信息
            aweme_item["risk_infos"] = self._build_risk_info(aweme_data.get("risk_infos", {}))

            # 设置可选字段为None（保持与类型定义一致）
            optional_fields = [
                "cha_list",
                "video_labels",
                "image_infos",
                "comment_list",
                "geofencing",
                "video_text",
                "label_top_text",
                "promotions",
                "long_video",
                "images",
                "chapter_list",
                "interaction_stickers",
                "img_bitrate",
                "chapter_bar_color",
            ]

            for field in optional_fields:
                aweme_item[field] = aweme_data.get(field)

            return aweme_item

        except Exception as e:
            logger.error(f"构建AwemeItem时发生错误: {e}")
            return None

    def _build_author_info(self, author_data: Dict[str, Any]) -> "AuthorInfo":
        """构建作者信息"""
        return {
            "short_id": str(author_data.get("short_id", "")),
            "nickname": str(author_data.get("nickname", "")),
            "signature": str(author_data.get("signature", "")),
            "avatar_thumb": self._build_url_info(author_data.get("avatar_thumb", {})),
            "avatar_medium": self._build_url_info(author_data.get("avatar_medium", {})),
            "follow_status": self._safe_int(author_data.get("follow_status", 0)),
            "following_count": self._safe_int(author_data.get("following_count", 0)),
            "favoriting_count": self._safe_int(author_data.get("favoriting_count", 0)),
            "unique_id": str(author_data.get("unique_id", "")),
            "mplatform_followers_count": self._safe_int(author_data.get("follower_count", 0)),
            "followers_detail": author_data.get("followers_detail"),
            "platform_sync_info": author_data.get("platform_sync_info"),
            "geofencing": author_data.get("geofencing"),
            "policy_version": author_data.get("policy_version"),
            "sec_uid": str(author_data.get("sec_uid", "")),
            "type_label": author_data.get("type_label"),
            "card_entries": author_data.get("card_entries"),
            "mix_info": author_data.get("mix_info"),
        }

    def _build_music_info(self, music_data: Dict[str, Any]) -> "MusicInfo":
        """构建音乐信息"""
        return {
            "mid": str(music_data.get("mid", music_data.get("id_str", ""))),
            "title": str(music_data.get("title", "")),
            "author": str(music_data.get("author", "")),
            "cover_hd": self._build_url_info(music_data.get("cover_hd", {})),
            "cover_large": self._build_url_info(music_data.get("cover_large", {})),
            "cover_medium": self._build_url_info(music_data.get("cover_medium", {})),
            "cover_thumb": self._build_url_info(music_data.get("cover_thumb", {})),
            "duration": self._safe_int(music_data.get("duration", 0)),
            "position": music_data.get("position"),
            "status": self._safe_int(music_data.get("status", 1)),
        }

    def _build_video_info(self, video_data: Dict[str, Any]) -> "VideoInfo":
        """构建视频信息"""
        return {
            "play_addr": self._build_url_info(video_data.get("play_addr", {})),
            "cover": self._build_url_info(video_data.get("cover", {})),
            "height": self._safe_int(video_data.get("height", 0)),
            "width": self._safe_int(video_data.get("width", 0)),
            "bit_rate": video_data.get("bit_rate"),
            "big_thumbs": video_data.get("big_thumbs"),
        }

    def _build_statistics_info(self, stats_data: Dict[str, Any], aweme_id: str) -> "StatisticsInfo":
        """构建统计信息"""
        return {
            "aweme_id": aweme_id,
            "comment_count": self._safe_int(stats_data.get("comment_count", 0)),
            "digg_count": self._safe_int(stats_data.get("digg_count", 0)),
            "play_count": self._safe_int(stats_data.get("play_count", 0)),
            "share_count": self._safe_int(stats_data.get("share_count", 0)),
            "collect_count": self._safe_int(stats_data.get("collect_count", 0)),
        }

    def _build_text_extra_info(self, text_extra_data: List[Dict[str, Any]]) -> List["TextExtraInfo"]:
        """构建文本额外信息"""
        if not isinstance(text_extra_data, list):
            return []

        result = []
        for item in text_extra_data:
            if isinstance(item, dict):
                text_extra_item: "TextExtraInfo" = {
                    "start": self._safe_int(item.get("start", 0)),
                    "end": self._safe_int(item.get("end", 0)),
                    "type": self._safe_int(item.get("type", 0)),
                    "hashtag_name": str(item.get("hashtag_name", "")),
                    "hashtag_id": self._safe_int(item.get("hashtag_id", 0)),
                }
                result.append(text_extra_item)

        return result

    def _build_risk_info(self, risk_data: Dict[str, Any]) -> "RiskInfo":
        """构建风险信息"""
        return {
            "warn": bool(risk_data.get("warn", False)),
            "type": self._safe_int(risk_data.get("type", 0)),
            "content": str(risk_data.get("content", "")),
            "reflow_unplayable": self._safe_int(risk_data.get("reflow_unplayable", 0)),
        }

    def _build_url_info(self, url_data: Dict[str, Any]) -> "UrlInfo":
        """构建URL信息"""
        url_list = url_data.get("url_list", [])
        if not isinstance(url_list, list):
            url_list = []

        return {
            "uri": str(url_data.get("uri", "")),
            "url_list": [str(url) for url in url_list if url],
        }

    def _safe_int(self, value: Any) -> int:
        """安全地转换为整数"""
        try:
            if isinstance(value, (int, float)):
                return int(value)
            elif isinstance(value, str):
                # 尝试转换字符串为整数
                return int(float(value))
            else:
                return 0
        except (ValueError, TypeError):
            return 0

    async def process_jingxuan_url(self, aweme_id: str) -> JingxuanProcessResult:
        """
        处理精选URL的完整流程，包含全面的错误处理和日志记录

        Args:
            aweme_id: 抖音视频ID

        Returns:
            JingxuanProcessResult: 处理结果
        """
        operation_start_time = time.time()
        context = create_error_context(aweme_id, "process_jingxuan_url")

        logger.info("[精选提取] 开始处理精选URL", extra=context)

        # 输入验证
        if not aweme_id or not isinstance(aweme_id, str):
            error_msg = "aweme_id不能为空且必须是字符串"
            logger.error(f"[精选提取] 输入验证失败: {error_msg}", extra=context)
            return JingxuanProcessResult(aweme_id=aweme_id or "unknown", success=False, error=error_msg)

        if not aweme_id.isdigit():
            error_msg = f"aweme_id格式无效，必须是数字: {aweme_id}"
            logger.error(f"[精选提取] aweme_id格式验证失败: {error_msg}", extra=context)
            return JingxuanProcessResult(aweme_id=aweme_id, success=False, error=error_msg)

        # 构建精选URL
        from rpc.douyin.html_handler.url_manager import DouyinURLManager

        jingxuan_url = DouyinURLManager.build_jingxuan_url(aweme_id)

        try:
            # 步骤1: 通过RPC获取视频数据（替代HTML获取）
            logger.info("[精选提取] 步骤1: 通过RPC获取视频数据", extra={**context, "aweme_id": aweme_id})
            try:
                video_data = await self.fetch_video_data(aweme_id)
                logger.info("[精选提取] 视频数据获取成功", extra={**context, "data_keys": len(video_data)})

                # 直接转换为AwemeItem格式，跳过HTML解析步骤
                try:
                    aweme_item = self.convert_to_aweme_item(video_data, aweme_id)
                    if aweme_item:
                        return JingxuanProcessResult(
                            aweme_id=aweme_id, success=True, raw_data=video_data, video_info=aweme_item
                        )
                    else:
                        error_msg = "视频数据转换失败"
                        logger.error(f"[精选提取] 数据转换失败: {error_msg}", extra=context)
                        return JingxuanProcessResult(aweme_id=aweme_id, success=False, error=error_msg)
                except Exception as e:
                    error_msg = f"数据转换异常: {str(e)}"
                    logger.error(f"[精选提取] 数据转换异常: {error_msg}", extra=context)
                    return JingxuanProcessResult(aweme_id=aweme_id, success=False, error=error_msg)

            except JingxuanExtractionError as e:
                logger.error(f"[精选提取] 视频数据获取失败: {e}", extra=context)
                return JingxuanProcessResult(aweme_id=aweme_id, success=False, error=f"视频数据获取失败: {str(e)}")
            except Exception as e:
                logger.error(f"[精选提取] 视频数据获取异常: {e}", extra=context)
                return JingxuanProcessResult(aweme_id=aweme_id, success=False, error=f"视频数据获取失败: {str(e)}")

            # 成功完成所有步骤
            success_context = {
                **context,
                "process_success": True,
                "total_processing_time": time.time() - operation_start_time,
                "final_aweme_id": aweme_item.get("aweme_id", "unknown"),
            }

            logger.info("[精选提取] 精选URL处理完成", extra=success_context)

            return JingxuanProcessResult(aweme_id=aweme_id, success=True, raw_data=json_data, video_info=aweme_item)

        except Exception as e:
            # 捕获任何未预期的错误
            error_msg = f"精选URL处理过程中发生未知错误: {e}"
            logger.error(
                "[精选提取] 未知错误",
                extra={
                    **context,
                    "error_detail": str(e),
                    "error_type": type(e).__name__,
                    "processing_time": time.time() - operation_start_time,
                },
            )

            return JingxuanProcessResult(aweme_id=aweme_id, success=False, error=error_msg)

    def compare_extraction_consistency(
        self,
        jingxuan_data: Dict[str, Any],
        other_data: Dict[str, Any],
        source: str = "unknown",
        aweme_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        比较精选提取数据与其他提取方法数据的一致性

        Args:
            jingxuan_data: 精选提取的数据
            other_data: 其他方法提取的数据
            source: 其他数据的来源（如"mobile_url"或"rpc_api"）
            aweme_id: 相关的视频ID（用于日志记录）

        Returns:
            包含一致性比较结果的字典
        """
        context = create_error_context(aweme_id or "unknown", "compare_extraction_consistency", source=source)
        logger.info("[精选提取] 开始比较数据一致性", extra=context)

        result = {"consistent": True, "differences": [], "match_score": 100.0, "critical_fields_match": True}

        try:
            # 如果任一数据为空，无法比较
            if not jingxuan_data or not other_data:
                logger.warning(
                    "[精选提取] 无法比较数据一致性，数据不完整",
                    extra={
                        **context,
                        "jingxuan_data_empty": not bool(jingxuan_data),
                        "other_data_empty": not bool(other_data),
                    },
                )
                result["consistent"] = False
                result["differences"].append("数据不完整，无法比较")
                result["match_score"] = 0.0
                result["critical_fields_match"] = False
                return result

            # 1. 比较关键字段
            critical_fields = ["aweme_id", "desc"]
            critical_differences = []

            for field in critical_fields:
                jingxuan_value = jingxuan_data.get(field)
                other_value = other_data.get(field)

                if jingxuan_value != other_value:
                    critical_differences.append(
                        {"field": field, "jingxuan_value": jingxuan_value, "other_value": other_value}
                    )

            if critical_differences:
                result["critical_fields_match"] = False
                result["differences"].extend(critical_differences)
                logger.warning(
                    "[精选提取] 关键字段不一致", extra={**context, "critical_differences": critical_differences}
                )

            # 2. 比较作者信息
            jingxuan_author = jingxuan_data.get("author", {})
            other_author = other_data.get("author", {})

            author_fields = ["nickname", "sec_uid", "unique_id"]
            author_differences = []

            for field in author_fields:
                jingxuan_value = jingxuan_author.get(field)
                other_value = other_author.get(field)

                if jingxuan_value != other_value:
                    author_differences.append(
                        {"field": f"author.{field}", "jingxuan_value": jingxuan_value, "other_value": other_value}
                    )

            if author_differences:
                result["differences"].extend(author_differences)
                logger.warning("[精选提取] 作者信息不一致", extra={**context, "author_differences": author_differences})

            # 3. 比较视频信息
            jingxuan_video = jingxuan_data.get("video", {})
            other_video = other_data.get("video", {})

            # 比较视频尺寸
            video_dimension_differences = []
            for dim in ["width", "height"]:
                jingxuan_dim = jingxuan_video.get(dim)
                other_dim = other_video.get(dim)

                # 允许尺寸有小的差异（不同清晰度可能导致）
                if jingxuan_dim and other_dim and abs(int(jingxuan_dim) - int(other_dim)) > 10:
                    video_dimension_differences.append(
                        {"field": f"video.{dim}", "jingxuan_value": jingxuan_dim, "other_value": other_dim}
                    )

            if video_dimension_differences:
                result["differences"].extend(video_dimension_differences)
                logger.warning(
                    "[精选提取] 视频尺寸不一致",
                    extra={**context, "video_dimension_differences": video_dimension_differences},
                )

            # 4. 比较统计信息（如果存在）
            jingxuan_stats = jingxuan_data.get("statistics", {})
            other_stats = other_data.get("statistics", {})

            # 统计数据可能有差异，只记录不影响一致性判断
            if jingxuan_stats and other_stats:
                stats_fields = ["comment_count", "digg_count", "play_count", "share_count"]
                stats_differences = []

                for field in stats_fields:
                    jingxuan_value = jingxuan_stats.get(field)
                    other_value = other_stats.get(field)

                    # 统计数据可能随时间变化，只记录显著差异
                    if jingxuan_value is not None and other_value is not None:
                        try:
                            jingxuan_int = int(jingxuan_value)
                            other_int = int(other_value)
                            # 如果差异超过20%，记录
                            if (
                                max(jingxuan_int, other_int) > 0
                                and abs(jingxuan_int - other_int) / max(jingxuan_int, other_int) > 0.2
                            ):
                                stats_differences.append(
                                    {
                                        "field": f"statistics.{field}",
                                        "jingxuan_value": jingxuan_value,
                                        "other_value": other_value,
                                    }
                                )
                        except (ValueError, TypeError):
                            # 如果不是数字，直接比较
                            if jingxuan_value != other_value:
                                stats_differences.append(
                                    {
                                        "field": f"statistics.{field}",
                                        "jingxuan_value": jingxuan_value,
                                        "other_value": other_value,
                                    }
                                )

                if stats_differences:
                    logger.info(
                        "[精选提取] 统计数据有差异（正常现象）",
                        extra={**context, "stats_differences": stats_differences},
                    )

            # 5. 计算总体匹配分数
            total_fields = len(critical_fields) + len(author_fields) + 2  # 2 for video dimensions
            matched_fields = (
                total_fields - len(critical_differences) - len(author_differences) - len(video_dimension_differences)
            )
            match_score = (matched_fields / total_fields) * 100 if total_fields > 0 else 0

            result["match_score"] = round(match_score, 2)
            result["consistent"] = match_score >= 80 and result["critical_fields_match"]

            # 记录最终结果
            logger.info(
                "[精选提取] 数据一致性比较完成",
                extra={
                    **context,
                    "match_score": result["match_score"],
                    "consistent": result["consistent"],
                    "differences_count": len(result["differences"]),
                },
            )

            return result

        except Exception as e:
            logger.error("[精选提取] 比较数据一致性时发生错误", extra={**context, "error": str(e)})
            result["consistent"] = False
            result["differences"].append(f"比较过程错误: {str(e)}")
            result["match_score"] = 0.0
            result["critical_fields_match"] = False
            return result

    def _convert_video_detail_to_aweme(self, video_detail: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        将精选页面的videoDetail结构转换为标准aweme数据结构

        Args:
            video_detail: 来自app.videoDetail的数据

        Returns:
            转换后的aweme数据结构，如果转换失败则返回None
        """
        try:
            if not isinstance(video_detail, dict):
                return None

            # 构建aweme数据结构
            aweme_data = {
                # 基本信息
                "aweme_id": str(video_detail.get("awemeId", "")),
                "desc": str(video_detail.get("desc", "")),
                "create_time": self._safe_int(video_detail.get("createTime", 0)),
                "aweme_type": self._safe_int(video_detail.get("awemeType", 0)),
                "group_id": str(video_detail.get("groupId", video_detail.get("awemeId", ""))),
                # 作者信息转换
                "author": self._convert_author_info(video_detail.get("authorInfo", {})),
                # 音乐信息转换
                "music": self._convert_music_info(video_detail.get("music", {})),
                # 视频信息转换
                "video": self._convert_video_info(video_detail.get("video", {})),
                # 统计信息转换
                "statistics": self._convert_stats_info(video_detail.get("stats", {})),
                # 文本额外信息
                "text_extra": video_detail.get("textExtra", []),
                # 风险信息
                "risk_infos": self._convert_risk_infos(video_detail.get("riskInfos", {})),
                # 下载信息
                "download": self._convert_download_info(video_detail.get("download", {})),
                # 其他字段
                "media_type": self._safe_int(video_detail.get("mediaType", 4)),
                "user_digged": video_detail.get("userDigged", False),
                "user_collected": video_detail.get("userCollected", False),
            }

            return aweme_data

        except Exception as e:
            logger.error(f"转换videoDetail数据时发生错误: {e}")
            return None

    def _convert_author_info(self, author_info: Dict[str, Any]) -> Dict[str, Any]:
        """转换作者信息"""
        if not isinstance(author_info, dict):
            return {}

        return {
            "uid": str(author_info.get("uid", "")),
            "sec_uid": str(author_info.get("secUid", "")),
            "nickname": str(author_info.get("nickname", "")),
            "avatar_thumb": self._convert_avatar_thumb(author_info.get("avatarThumb", {})),
            "avatar_uri": str(author_info.get("avatarUri", "")),
            "follower_count": self._safe_int(author_info.get("followerCount", 0)),
            "total_favorited": self._safe_int(author_info.get("totalFavorited", 0)),
            "follow_status": self._safe_int(author_info.get("followStatus", 0)),
            "follower_status": self._safe_int(author_info.get("followerStatus", 0)),
            "enterprise_verify_reason": str(author_info.get("enterpriseVerifyReason", "")),
            "custom_verify": str(author_info.get("customVerify", "")),
            "secret": self._safe_int(author_info.get("secret", 0)),
        }

    def _convert_avatar_thumb(self, avatar_thumb: Dict[str, Any]) -> Dict[str, Any]:
        """转换头像缩略图信息"""
        if not isinstance(avatar_thumb, dict):
            return {}

        return {
            "height": self._safe_int(avatar_thumb.get("height", 0)),
            "width": self._safe_int(avatar_thumb.get("width", 0)),
            "uri": str(avatar_thumb.get("uri", "")),
            "url_list": avatar_thumb.get("urlList", []),
        }

    def _convert_music_info(self, music: Dict[str, Any]) -> Dict[str, Any]:
        """转换音乐信息"""
        if not isinstance(music, dict):
            return {}

        return {
            "id": self._safe_int(music.get("id", 0)),
            "id_str": str(music.get("idStr", "")),
            "title": str(music.get("title", "")),
            "author": str(music.get("author", "")),
            "album": str(music.get("album", "")),
            "cover_thumb": self._convert_cover_info(music.get("coverThumb", {})),
            "cover_medium": self._convert_cover_info(music.get("coverMedium", {})),
            "play_url": self._convert_play_url(music.get("playUrl", {})),
            "sec_uid": str(music.get("secUid", "")),
            "duration": self._safe_int(music.get("duration", 0)),
            "is_original": music.get("isOriginal", False),
            "user_count": self._safe_int(music.get("userCount", 0)),
        }

    def _convert_cover_info(self, cover_info: Dict[str, Any]) -> Dict[str, Any]:
        """转换封面信息"""
        if not isinstance(cover_info, dict):
            return {}

        return {
            "uri": str(cover_info.get("uri", "")),
            "url_list": cover_info.get("urlList", []),
            "width": self._safe_int(cover_info.get("width", 0)),
            "height": self._safe_int(cover_info.get("height", 0)),
        }

    def _convert_play_url(self, play_url: Dict[str, Any]) -> Dict[str, Any]:
        """转换播放地址信息"""
        if not isinstance(play_url, dict):
            return {}

        return {
            "uri": str(play_url.get("uri", "")),
            "url_list": play_url.get("urlList", []),
            "width": self._safe_int(play_url.get("width", 0)),
            "height": self._safe_int(play_url.get("height", 0)),
            "url_key": str(play_url.get("urlKey", "")),
        }

    def _convert_video_info(self, video: Dict[str, Any]) -> Dict[str, Any]:
        """转换视频信息"""
        if not isinstance(video, dict):
            return {}

        return {
            "width": self._safe_int(video.get("width", 0)),
            "height": self._safe_int(video.get("height", 0)),
            "ratio": str(video.get("ratio", "")),
            "duration": self._safe_int(video.get("duration", 0)),
            "data_size": self._safe_int(video.get("dataSize", 0)),
            "uri": str(video.get("uri", "")),
            "play_addr": video.get("playAddr", []),
            "play_addr_size": self._safe_int(video.get("playAddrSize", 0)),
            "play_addr_file_hash": str(video.get("playAddrFileHash", "")),
            "play_api": str(video.get("playApi", "")),
            "bit_rate_list": video.get("bitRateList", []),
            "cover": str(video.get("cover", "")),
            "cover_url_list": video.get("coverUrlList", []),
            "cover_uri": str(video.get("coverUri", "")),
            "dynamic_cover": str(video.get("dynamicCover", "")),
            "origin_cover": str(video.get("originCover", "")),
            "origin_cover_url_list": video.get("originCoverUrlList", []),
            "gaussian_cover": str(video.get("gaussianCover", "")),
            "meta": video.get("meta", {}),
        }

    def _convert_stats_info(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """转换统计信息"""
        if not isinstance(stats, dict):
            return {}

        return {
            "aweme_id": str(stats.get("aweme_id", "")),
            "comment_count": self._safe_int(stats.get("commentCount", 0)),
            "digg_count": self._safe_int(stats.get("diggCount", 0)),
            "download_count": self._safe_int(stats.get("downloadCount", 0)),
            "play_count": self._safe_int(stats.get("playCount", 0)),
            "share_count": self._safe_int(stats.get("shareCount", 0)),
            "forward_count": self._safe_int(stats.get("forwardCount", 0)),
            "collect_count": self._safe_int(stats.get("collectCount", 0)),
        }

    def _convert_risk_infos(self, risk_infos: Dict[str, Any]) -> Dict[str, Any]:
        """转换风险信息"""
        if not isinstance(risk_infos, dict):
            return {}

        return {
            "warn": risk_infos.get("warn", False),
            "type": self._safe_int(risk_infos.get("type", 0)),
            "content": str(risk_infos.get("content", "")),
        }

    def _convert_download_info(self, download: Dict[str, Any]) -> Dict[str, Any]:
        """转换下载信息"""
        if not isinstance(download, dict):
            return {}

        return {
            "url": str(download.get("url", "")),
            "url_list": download.get("urlList", []),
            "allow_download": download.get("allowDownload", False),
        }
